"""
Utility functions for the spaced repetition system.

This module contains helper functions for file I/O, data validation,
formatting, and other common operations.
"""

import csv
import logging
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)


def validate_deck_name(name: str) -> Tuple[bool, str]:
    """
    Validate a deck name.
    
    Args:
        name: Deck name to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not name or not name.strip():
        return False, "Deck name cannot be empty"
    
    name = name.strip()
    
    if len(name) > 255:
        return False, "Deck name too long (max 255 characters)"
    
    # Check for invalid characters (basic validation)
    if re.search(r'[<>:"/\\|?*]', name):
        return False, "Deck name contains invalid characters"
    
    return True, ""


def validate_card_content(front: str, back: str) -> Tuple[bool, str]:
    """
    Validate card front and back content.
    
    Args:
        front: Front side text
        back: Back side text
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not front or not front.strip():
        return False, "Card front cannot be empty"
    
    if not back or not back.strip():
        return False, "Card back cannot be empty"
    
    if len(front.strip()) > 10000:
        return False, "Card front too long (max 10000 characters)"
    
    if len(back.strip()) > 10000:
        return False, "Card back too long (max 10000 characters)"
    
    return True, ""


def parse_csv_file(file_path: str, delimiter: str = ',') -> List[Dict[str, str]]:
    """
    Parse a CSV file and return card data.
    
    Expected format:
    front,back
    "Question 1","Answer 1"
    "Question 2","Answer 2"
    
    Args:
        file_path: Path to CSV file
        delimiter: CSV delimiter (default: comma)
        
    Returns:
        List of dictionaries with 'front' and 'back' keys
        
    Raises:
        FileNotFoundError: If file doesn't exist
        ValueError: If file format is invalid
    """
    file_path = Path(file_path)
    if not file_path.exists():
        raise FileNotFoundError(f"File not found: {file_path}")
    
    cards = []
    
    try:
        with open(file_path, 'r', encoding='utf-8', newline='') as csvfile:
            # Detect if file has header
            sample = csvfile.read(1024)
            csvfile.seek(0)
            
            sniffer = csv.Sniffer()
            has_header = sniffer.has_header(sample)
            
            reader = csv.DictReader(csvfile, delimiter=delimiter)
            
            # If no header detected, assume first row is data
            if not has_header:
                csvfile.seek(0)
                reader = csv.DictReader(csvfile, fieldnames=['front', 'back'], delimiter=delimiter)
            
            for row_num, row in enumerate(reader, start=1):
                # Handle different possible column names
                front = row.get('front') or row.get('Front') or row.get('question') or row.get('Question')
                back = row.get('back') or row.get('Back') or row.get('answer') or row.get('Answer')
                
                if not front or not back:
                    logger.warning(f"Row {row_num}: Missing front or back content, skipping")
                    continue
                
                # Validate card content
                is_valid, error = validate_card_content(front, back)
                if not is_valid:
                    logger.warning(f"Row {row_num}: {error}, skipping")
                    continue
                
                cards.append({
                    'front': front.strip(),
                    'back': back.strip()
                })
        
        logger.info(f"Parsed {len(cards)} cards from {file_path}")
        return cards
        
    except UnicodeDecodeError:
        raise ValueError(f"File encoding error. Please ensure {file_path} is UTF-8 encoded.")
    except csv.Error as e:
        raise ValueError(f"CSV parsing error: {e}")


def export_cards_to_csv(cards: List[Dict[str, Any]], file_path: str) -> bool:
    """
    Export cards to a CSV file.
    
    Args:
        cards: List of card dictionaries
        file_path: Output file path
        
    Returns:
        True if export successful
    """
    try:
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8', newline='') as csvfile:
            fieldnames = ['front', 'back']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for card in cards:
                writer.writerow({
                    'front': card.get('front', ''),
                    'back': card.get('back', '')
                })
        
        logger.info(f"Exported {len(cards)} cards to {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error exporting cards to {file_path}: {e}")
        return False


def format_duration(seconds: float) -> str:
    """
    Format duration in seconds to human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds:.1f} seconds"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f} minutes"
    else:
        hours = seconds / 3600
        return f"{hours:.1f} hours"


def format_datetime(dt: datetime, format_type: str = 'short') -> str:
    """
    Format datetime for display.
    
    Args:
        dt: Datetime to format
        format_type: 'short', 'long', or 'relative'
        
    Returns:
        Formatted datetime string
    """
    if format_type == 'short':
        return dt.strftime('%Y-%m-%d %H:%M')
    elif format_type == 'long':
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    elif format_type == 'relative':
        now = datetime.now()
        diff = now - dt
        
        if diff.days > 0:
            return f"{diff.days} days ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hours ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minutes ago"
        else:
            return "Just now"
    else:
        return dt.isoformat()


def format_card_counts(counts: Dict[str, int]) -> str:
    """
    Format card counts for display.
    
    Args:
        counts: Dictionary with 'total', 'due', 'new' counts
        
    Returns:
        Formatted string like "5 due, 10 new (15 total)"
    """
    due = counts.get('due', 0)
    new = counts.get('new', 0)
    total = counts.get('total', 0)
    
    parts = []
    if due > 0:
        parts.append(f"{due} due")
    if new > 0:
        parts.append(f"{new} new")
    
    if parts:
        result = ", ".join(parts)
        if total > due + new:
            result += f" ({total} total)"
        return result
    elif total > 0:
        return f"{total} total"
    else:
        return "empty"


def truncate_text(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """
    Truncate text to maximum length with suffix.
    
    Args:
        text: Text to truncate
        max_length: Maximum length including suffix
        suffix: Suffix to add when truncating
        
    Returns:
        Truncated text
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def sanitize_filename(filename: str) -> str:
    """
    Sanitize a filename by removing invalid characters.
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    # Remove invalid characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing whitespace and dots
    sanitized = sanitized.strip(' .')
    
    # Ensure not empty
    if not sanitized:
        sanitized = "untitled"
    
    return sanitized


def get_file_size_mb(file_path: str) -> float:
    """
    Get file size in megabytes.
    
    Args:
        file_path: Path to file
        
    Returns:
        File size in MB
    """
    try:
        size_bytes = Path(file_path).stat().st_size
        return size_bytes / (1024 * 1024)
    except (OSError, FileNotFoundError):
        return 0.0


def ensure_directory(dir_path: str) -> bool:
    """
    Ensure directory exists, create if necessary.
    
    Args:
        dir_path: Directory path
        
    Returns:
        True if directory exists or was created successfully
    """
    try:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"Error creating directory {dir_path}: {e}")
        return False


def clean_text(text: str) -> str:
    """
    Clean text by removing extra whitespace and normalizing.
    
    Args:
        text: Text to clean
        
    Returns:
        Cleaned text
    """
    if not text:
        return ""
    
    # Normalize whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove control characters except newlines and tabs
    text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    
    return text
