"""
Configuration management for the spaced repetition system.

This module handles configuration loading from environment variables,
configuration files, and default settings.
"""

import os
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from dataclasses import dataclass, field
import json

logger = logging.getLogger(__name__)


@dataclass
class SRSConfig:
    """
    Configuration settings for the SRS application.
    
    Attributes:
        database_path: Path to SQLite database file
        config_path: Path to configuration file
        log_level: Logging level
        log_file: Optional log file path
        backup_enabled: Whether to enable automatic backups
        backup_interval_hours: Hours between automatic backups
        max_backup_files: Maximum number of backup files to keep
    """
    database_path: str = ""
    config_path: str = ""
    log_level: str = "INFO"
    log_file: Optional[str] = None
    backup_enabled: bool = True
    backup_interval_hours: int = 24
    max_backup_files: int = 7
    
    # Performance settings
    query_timeout_seconds: float = 30.0
    connection_pool_size: int = 5
    
    # UI settings
    progress_bar_width: int = 50
    max_display_length: int = 80
    
    def __post_init__(self):
        """Set default paths if not provided."""
        if not self.database_path:
            self.database_path = self._get_default_database_path()
        
        if not self.config_path:
            self.config_path = self._get_default_config_path()
    
    def _get_default_database_path(self) -> str:
        """Get default database path."""
        srs_dir = Path.home() / ".srs"
        return str(srs_dir / "data.db")
    
    def _get_default_config_path(self) -> str:
        """Get default config file path."""
        return str(Path.home() / ".srsrc")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            'database_path': self.database_path,
            'config_path': self.config_path,
            'log_level': self.log_level,
            'log_file': self.log_file,
            'backup_enabled': self.backup_enabled,
            'backup_interval_hours': self.backup_interval_hours,
            'max_backup_files': self.max_backup_files,
            'query_timeout_seconds': self.query_timeout_seconds,
            'connection_pool_size': self.connection_pool_size,
            'progress_bar_width': self.progress_bar_width,
            'max_display_length': self.max_display_length
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SRSConfig':
        """Create config from dictionary."""
        return cls(**{k: v for k, v in data.items() if hasattr(cls, k)})


class ConfigManager:
    """
    Manages configuration loading and saving.
    
    Configuration priority (highest to lowest):
    1. Environment variables
    2. Configuration file
    3. Default values
    """
    
    def __init__(self):
        """Initialize configuration manager."""
        self._config: Optional[SRSConfig] = None
    
    def load_config(self, config_path: Optional[str] = None) -> SRSConfig:
        """
        Load configuration from various sources.
        
        Args:
            config_path: Optional path to config file
            
        Returns:
            Loaded configuration
        """
        # Start with defaults
        config = SRSConfig()
        
        # Override config_path if provided
        if config_path:
            config.config_path = config_path
        
        # Load from config file
        config = self._load_from_file(config)
        
        # Override with environment variables
        config = self._load_from_env(config)
        
        self._config = config
        logger.info(f"Configuration loaded: database={config.database_path}")
        return config
    
    def _load_from_file(self, config: SRSConfig) -> SRSConfig:
        """Load configuration from file."""
        config_file = Path(config.config_path)
        
        if not config_file.exists():
            logger.debug(f"Config file not found: {config_file}")
            return config
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                if config_file.suffix.lower() == '.json':
                    data = json.load(f)
                else:
                    # Simple key=value format
                    data = {}
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            if '=' in line:
                                key, value = line.split('=', 1)
                                data[key.strip()] = value.strip()
            
            # Update config with file data
            for key, value in data.items():
                if hasattr(config, key):
                    # Convert string values to appropriate types
                    current_value = getattr(config, key)
                    if isinstance(current_value, bool):
                        value = value.lower() in ('true', '1', 'yes', 'on')
                    elif isinstance(current_value, int):
                        value = int(value)
                    elif isinstance(current_value, float):
                        value = float(value)
                    
                    setattr(config, key, value)
            
            logger.info(f"Loaded configuration from {config_file}")
            
        except Exception as e:
            logger.warning(f"Error loading config file {config_file}: {e}")
        
        return config
    
    def _load_from_env(self, config: SRSConfig) -> SRSConfig:
        """Load configuration from environment variables."""
        env_mappings = {
            'SRS_DATABASE_PATH': 'database_path',
            'SRS_CONFIG_PATH': 'config_path',
            'SRS_LOG_LEVEL': 'log_level',
            'SRS_LOG_FILE': 'log_file',
            'SRS_BACKUP_ENABLED': 'backup_enabled',
            'SRS_BACKUP_INTERVAL_HOURS': 'backup_interval_hours',
            'SRS_MAX_BACKUP_FILES': 'max_backup_files',
            'SRS_QUERY_TIMEOUT_SECONDS': 'query_timeout_seconds',
            'SRS_CONNECTION_POOL_SIZE': 'connection_pool_size',
            'SRS_PROGRESS_BAR_WIDTH': 'progress_bar_width',
            'SRS_MAX_DISPLAY_LENGTH': 'max_display_length'
        }
        
        for env_var, config_attr in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                # Convert to appropriate type
                current_value = getattr(config, config_attr)
                if isinstance(current_value, bool):
                    value = value.lower() in ('true', '1', 'yes', 'on')
                elif isinstance(current_value, int):
                    value = int(value)
                elif isinstance(current_value, float):
                    value = float(value)
                
                setattr(config, config_attr, value)
                logger.debug(f"Set {config_attr} from environment: {value}")
        
        return config
    
    def save_config(self, config: SRSConfig, config_path: Optional[str] = None) -> bool:
        """
        Save configuration to file.
        
        Args:
            config: Configuration to save
            config_path: Optional path to save to
            
        Returns:
            True if saved successfully
        """
        save_path = config_path or config.config_path
        config_file = Path(save_path)
        
        try:
            # Ensure directory exists
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Save as JSON for better structure
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config.to_dict(), f, indent=2)
            
            logger.info(f"Configuration saved to {config_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving config to {config_file}: {e}")
            return False
    
    def get_config(self) -> SRSConfig:
        """Get current configuration."""
        if self._config is None:
            self._config = self.load_config()
        return self._config


# Global configuration manager
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """Get the global configuration manager."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def get_config() -> SRSConfig:
    """Get the current configuration."""
    return get_config_manager().get_config()


def load_config(config_path: Optional[str] = None) -> SRSConfig:
    """Load configuration from file and environment."""
    return get_config_manager().load_config(config_path)


def save_config(config: SRSConfig, config_path: Optional[str] = None) -> bool:
    """Save configuration to file."""
    return get_config_manager().save_config(config, config_path)


def setup_logging(config: SRSConfig):
    """
    Set up logging based on configuration.
    
    Args:
        config: Configuration with logging settings
    """
    # Convert log level string to logging constant
    log_level = getattr(logging, config.log_level.upper(), logging.INFO)
    
    # Configure logging format
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Configure handlers
    handlers = []
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(log_format))
    handlers.append(console_handler)
    
    # File handler if specified
    if config.log_file:
        try:
            log_file = Path(config.log_file)
            log_file.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(logging.Formatter(log_format))
            handlers.append(file_handler)
        except Exception as e:
            print(f"Warning: Could not set up file logging: {e}")
    
    # Configure root logger
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=handlers,
        force=True  # Override any existing configuration
    )
    
    logger.info(f"Logging configured: level={config.log_level}, file={config.log_file}")


def reset_config():
    """Reset the global configuration manager (mainly for testing)."""
    global _config_manager
    _config_manager = None
