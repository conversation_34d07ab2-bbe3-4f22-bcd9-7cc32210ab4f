"""
Command-line interface for the spaced repetition system.

This module implements the CLI commands for deck management, card management,
review sessions, and system utilities.
"""

import sys
import argparse
import logging
import signal
from typing import Optional
from pathlib import Path

from . import initialize_srs, __version__
from .models import Deck, Card
from .review import create_review_session
from .utils import (
    validate_deck_name, validate_card_content, parse_csv_file,
    format_card_counts, format_duration, truncate_text
)

logger = logging.getLogger(__name__)


# UI Helper Functions

def print_header(title: str, width: int = 60) -> None:
    """Print a formatted header."""
    print()
    print("=" * width)
    print(f" {title} ".center(width))
    print("=" * width)
    print()


def print_separator(width: int = 60) -> None:
    """Print a separator line."""
    print("-" * width)


def print_success(message: str) -> None:
    """Print a success message."""
    print(f"✅ {message}")


def print_error(message: str) -> None:
    """Print an error message."""
    print(f"❌ {message}")


def print_warning(message: str) -> None:
    """Print a warning message."""
    print(f"⚠️  {message}")


def print_info(message: str) -> None:
    """Print an info message."""
    print(f"ℹ️  {message}")


def confirm_action(message: str, default: bool = False) -> bool:
    """Ask user for confirmation."""
    suffix = " [y/N]" if not default else " [Y/n]"
    try:
        response = input(f"{message}{suffix}: ").strip().lower()
        if not response:
            return default
        return response in ['y', 'yes']
    except (EOFError, KeyboardInterrupt):
        print()
        return False


def get_user_input(prompt: str, required: bool = True) -> Optional[str]:
    """Get user input with optional validation."""
    try:
        while True:
            value = input(f"{prompt}: ").strip()
            if value or not required:
                return value if value else None
            print_error("This field is required. Please enter a value.")
    except (EOFError, KeyboardInterrupt):
        print()
        return None


def setup_signal_handlers():
    """Set up signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        del signum, frame  # Unused parameters
        print("\n\nOperation cancelled by user.")
        sys.exit(1)

    signal.signal(signal.SIGINT, signal_handler)
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)


class CLIError(Exception):
    """Custom exception for CLI errors."""
    pass


def setup_argument_parser() -> argparse.ArgumentParser:
    """Set up the main argument parser with all commands."""
    parser = argparse.ArgumentParser(
        prog='srs',
        description='Spaced Repetition System - A terminal-based flashcard application',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  srs create-deck "Spanish Verbs"
  srs add-card "Spanish Verbs"
  srs import-cards "Spanish Verbs" cards.csv
  srs review "Spanish Verbs"
  srs status
        """
    )

    parser.add_argument(
        '--version',
        action='version',
        version=f'SRS {__version__}'
    )

    parser.add_argument(
        '--config',
        help='Path to configuration file',
        metavar='PATH'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )

    # Create subparsers for commands
    subparsers = parser.add_subparsers(
        dest='command',
        help='Available commands',
        metavar='COMMAND'
    )

    # Deck management commands
    _add_deck_commands(subparsers)

    # Card management commands
    _add_card_commands(subparsers)

    # Review commands
    _add_review_commands(subparsers)

    # Utility commands
    _add_utility_commands(subparsers)

    return parser


def _add_deck_commands(subparsers):
    """Add deck management commands."""
    # create-deck command
    create_deck_parser = subparsers.add_parser(
        'create-deck',
        help='Create a new deck',
        description='Create a new flashcard deck with the specified name.'
    )
    create_deck_parser.add_argument(
        'name',
        help='Name of the deck to create'
    )

    # list-decks command
    list_decks_parser = subparsers.add_parser(
        'list-decks',
        help='List all decks',
        description='Show all decks with card counts and statistics.'
    )
    list_decks_parser.add_argument(
        '--detailed', '-d',
        action='store_true',
        help='Show detailed information for each deck'
    )

    # delete-deck command
    delete_deck_parser = subparsers.add_parser(
        'delete-deck',
        help='Delete a deck and all its cards',
        description='Delete a deck and all its cards. This action cannot be undone.'
    )
    delete_deck_parser.add_argument(
        'name',
        help='Name of the deck to delete'
    )
    delete_deck_parser.add_argument(
        '--force', '-f',
        action='store_true',
        help='Skip confirmation prompt'
    )


def _add_card_commands(subparsers):
    """Add card management commands."""
    # add-card command
    add_card_parser = subparsers.add_parser(
        'add-card',
        help='Add a card to a deck',
        description='Interactively add a new flashcard to the specified deck.'
    )
    add_card_parser.add_argument(
        'deck',
        help='Name of the deck to add the card to'
    )
    add_card_parser.add_argument(
        '--front',
        help='Front side of the card (if not provided, will prompt)'
    )
    add_card_parser.add_argument(
        '--back',
        help='Back side of the card (if not provided, will prompt)'
    )

    # import-cards command
    import_cards_parser = subparsers.add_parser(
        'import-cards',
        help='Import cards from a CSV/TSV file',
        description='Import multiple cards from a CSV or TSV file.'
    )
    import_cards_parser.add_argument(
        'deck',
        help='Name of the deck to import cards into'
    )
    import_cards_parser.add_argument(
        'file',
        help='Path to CSV/TSV file containing cards'
    )
    import_cards_parser.add_argument(
        '--delimiter',
        default=',',
        help='Field delimiter (default: comma)'
    )
    import_cards_parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be imported without actually importing'
    )

    # list-cards command
    list_cards_parser = subparsers.add_parser(
        'list-cards',
        help='List cards in a deck',
        description='Show all cards in the specified deck.'
    )
    list_cards_parser.add_argument(
        'deck',
        help='Name of the deck to list cards from'
    )
    list_cards_parser.add_argument(
        '--limit', '-l',
        type=int,
        default=20,
        help='Maximum number of cards to show (default: 20)'
    )
    list_cards_parser.add_argument(
        '--due-only',
        action='store_true',
        help='Show only cards that are due for review'
    )


def _add_review_commands(subparsers):
    """Add review commands."""
    # review command
    review_parser = subparsers.add_parser(
        'review',
        help='Start a review session',
        description='Start an interactive review session for the specified deck.'
    )
    review_parser.add_argument(
        'deck',
        help='Name of the deck to review'
    )
    review_parser.add_argument(
        '--limit', '-l',
        type=int,
        help='Maximum number of cards to review'
    )


def _add_utility_commands(subparsers):
    """Add utility commands."""
    # status command
    status_parser = subparsers.add_parser(
        'status',
        help='Show system status',
        description='Show overview of all decks and due cards.'
    )
    status_parser.add_argument(
        '--detailed', '-d',
        action='store_true',
        help='Show detailed statistics'
    )


# Command implementation functions

def cmd_create_deck(args) -> int:
    """Create a new deck."""
    try:
        # Validate deck name
        is_valid, error_msg = validate_deck_name(args.name)
        if not is_valid:
            print_error(error_msg)
            return 1

        # Check for existing deck
        existing_deck = Deck.get_by_name(args.name)
        if existing_deck:
            print_error(f"Deck '{args.name}' already exists")
            return 1

        # Create the deck
        deck = Deck.create(args.name)
        print_success(f"Created deck '{deck.name}'")
        print_info(f"You can now add cards with: srs add-card \"{deck.name}\"")
        return 0

    except ValueError as e:
        print_error(str(e))
        return 1
    except Exception as e:
        logger.error(f"Unexpected error creating deck: {e}")
        print_error(f"Failed to create deck '{args.name}'")
        return 1


def cmd_list_decks(args) -> int:
    """List all decks."""
    try:
        decks = Deck.get_all()

        if not decks:
            print_header("Your Decks")
            print_info("No decks found")
            print("Get started by creating a deck:")
            print("  srs create-deck \"My First Deck\"")
            print()
            return 0

        print_header(f"Your Decks ({len(decks)} total)")

        total_cards = 0
        total_due = 0
        total_new = 0

        for i, deck in enumerate(decks, 1):
            counts = deck.get_card_counts()
            count_str = format_card_counts(counts)

            total_cards += counts['total']
            total_due += counts['due']
            total_new += counts['new']

            if args.detailed:
                print(f"{i:2d}. 📚 {deck.name}")
                print(f"     Cards: {count_str}")
                print(f"     Created: {deck.created_at.strftime('%Y-%m-%d %H:%M')}")
                if counts['due'] > 0:
                    print(f"     📅 {counts['due']} cards ready for review")
                print()
            else:
                status_icon = "🔥" if counts['due'] > 0 else "📚"
                print(f"{i:2d}. {status_icon} {deck.name:<30} {count_str}")

        if not args.detailed:
            print()

        print_separator()
        print(f"Total: {total_cards} cards, {total_due} due, {total_new} new")

        if total_due > 0:
            print()
            print_info(f"You have {total_due} cards ready for review!")
            print("Start reviewing with: srs review \"<deck-name>\"")

        return 0

    except Exception as e:
        logger.error(f"Error listing decks: {e}")
        print_error("Failed to list decks")
        return 1


def cmd_delete_deck(args) -> int:
    """Delete a deck and all its cards."""
    try:
        # Find the deck
        deck = Deck.get_by_name(args.name)
        if not deck:
            print_error(f"Deck '{args.name}' not found")
            print_info("Use 'srs list-decks' to see available decks")
            return 1

        # Get card count for confirmation
        counts = deck.get_card_counts()

        # Show deck information
        print_header(f"Delete Deck: {deck.name}")
        print(f"📚 Deck: {deck.name}")
        print(f"📊 Cards: {format_card_counts(counts)}")
        print(f"📅 Created: {deck.created_at.strftime('%Y-%m-%d %H:%M')}")
        print()

        # Confirm deletion unless --force is used
        if not args.force:
            print_warning("This action cannot be undone!")
            if counts['total'] > 0:
                print(f"This will permanently delete {counts['total']} cards.")

            confirmed = confirm_action(
                f"Are you sure you want to delete deck '{deck.name}'?",
                default=False
            )

            if not confirmed:
                print_info("Deletion cancelled")
                return 0

        # Delete the deck
        if deck.delete():
            print_success(f"Deleted deck '{args.name}' and {counts['total']} cards")
            return 0
        else:
            print_error(f"Failed to delete deck '{args.name}'")
            return 1

    except Exception as e:
        logger.error(f"Error deleting deck: {e}")
        print_error(f"Failed to delete deck '{args.name}'")
        return 1


def cmd_add_card(args) -> int:
    """Add a card to a deck."""
    try:
        # Find the deck
        deck = Deck.get_by_name(args.deck)
        if not deck:
            print_error(f"Deck '{args.deck}' not found")
            print_info("Use 'srs list-decks' to see available decks")
            return 1

        # Get card content
        if args.front and args.back:
            front = args.front
            back = args.back
        else:
            print_header(f"Add Card to: {deck.name}")
            print("Enter the card content (press Ctrl+C to cancel):")
            print()

            front = get_user_input("Front side", required=True)
            if front is None:
                print_info("Card creation cancelled")
                return 0

            back = get_user_input("Back side", required=True)
            if back is None:
                print_info("Card creation cancelled")
                return 0

        # Validate card content
        is_valid, error_msg = validate_card_content(front, back)
        if not is_valid:
            print_error(error_msg)
            return 1

        # Create the card
        Card.create(deck.id, front, back)
        print()
        print_success("Card added successfully!")
        print(f"📝 Front: {truncate_text(front, 50)}")
        print(f"📝 Back:  {truncate_text(back, 50)}")

        # Show deck stats
        counts = deck.get_card_counts()
        print()
        print_info(f"Deck '{deck.name}' now has {counts['total']} cards")

        return 0

    except Exception as e:
        logger.error(f"Error adding card: {e}")
        print_error("Failed to add card")
        return 1


def cmd_import_cards(args) -> int:
    """Import cards from a CSV/TSV file."""
    try:
        # Find the deck
        deck = Deck.get_by_name(args.deck)
        if not deck:
            print_error(f"Deck '{args.deck}' not found")
            print_info("Use 'srs list-decks' to see available decks")
            return 1

        # Check if file exists
        file_path = Path(args.file)
        if not file_path.exists():
            print_error(f"File '{args.file}' not found")
            return 1

        print_header(f"Import Cards to: {deck.name}")
        print(f"📁 File: {file_path.name}")
        print(f"📊 Delimiter: '{args.delimiter}'")
        print()

        # Parse the file
        try:
            cards_data = parse_csv_file(args.file, args.delimiter)
        except Exception as e:
            print_error(f"Failed to parse file: {e}")
            print_info("Make sure the file has 'front,back' format")
            return 1

        if not cards_data:
            print_warning("No valid cards found in file")
            return 1

        # Show what will be imported
        print_info(f"Found {len(cards_data)} card(s) to import")

        if args.dry_run:
            print_header("Dry Run Preview")
            for i, card_data in enumerate(cards_data[:5]):
                front = truncate_text(card_data['front'], 30)
                back = truncate_text(card_data['back'], 30)
                print(f"  {i+1:2d}. {front:<32} → {back}")
            if len(cards_data) > 5:
                print(f"  ... and {len(cards_data) - 5} more cards")
            print()
            print_info("Use the command without --dry-run to import these cards")
            return 0

        # Confirm import
        if not confirm_action(f"Import {len(cards_data)} cards into '{deck.name}'?", default=True):
            print_info("Import cancelled")
            return 0

        # Import the cards
        print()
        print("Importing cards...")
        imported_count = 0
        failed_cards = []

        for i, card_data in enumerate(cards_data):
            try:
                Card.create(deck.id, card_data['front'], card_data['back'])
                imported_count += 1
                if (i + 1) % 10 == 0:  # Progress indicator
                    print(f"  Imported {i + 1}/{len(cards_data)} cards...")
            except Exception as e:
                logger.warning(f"Failed to import card: {e}")
                failed_cards.append((i + 1, str(e)))

        print()
        print_success(f"Imported {imported_count} cards into '{deck.name}'")

        if failed_cards:
            print_warning(f"{len(failed_cards)} cards failed to import:")
            for line_num, error in failed_cards[:3]:  # Show first 3 errors
                print(f"  Line {line_num}: {error}")
            if len(failed_cards) > 3:
                print(f"  ... and {len(failed_cards) - 3} more errors")

        # Show updated deck stats
        counts = deck.get_card_counts()
        print()
        print_info(f"Deck '{deck.name}' now has {counts['total']} cards")

        return 0

    except Exception as e:
        logger.error(f"Error importing cards: {e}")
        print_error("Failed to import cards")
        return 1


def cmd_list_cards(args) -> int:
    """List cards in a deck."""
    try:
        # Find the deck
        deck = Deck.get_by_name(args.deck)
        if not deck:
            print(f"Error: Deck '{args.deck}' not found")
            return 1

        # Get cards
        if args.due_only:
            cards = Card.get_due_cards(deck.id)
            card_type = "due"
        else:
            cards = Card.get_by_deck(deck.id)
            card_type = "total"

        if not cards:
            if args.due_only:
                print(f"No due cards in deck '{deck.name}'")
            else:
                print(f"No cards in deck '{deck.name}'. Add cards with 'srs add-card \"{deck.name}\"'")
            return 0

        # Apply limit
        display_cards = cards[:args.limit] if args.limit else cards

        print(f"Showing {len(display_cards)} {card_type} card(s) from '{deck.name}':")
        print()

        for i, card in enumerate(display_cards, 1):
            front = truncate_text(card.front, 40)
            back = truncate_text(card.back, 40)

            # Show scheduling info
            if card.repetitions > 0:
                interval_days = card.interval / (24 * 60)
                due_str = card.due_date.strftime('%Y-%m-%d %H:%M')
                print(f"{i:3d}. {front} → {back}")
                print(f"      Reps: {card.repetitions}, Interval: {interval_days:.1f}d, Due: {due_str}")
            else:
                print(f"{i:3d}. {front} → {back} (new)")
            print()

        if len(cards) > len(display_cards):
            print(f"... and {len(cards) - len(display_cards)} more cards")

        return 0

    except Exception as e:
        logger.error(f"Error listing cards: {e}")
        print("Error: Failed to list cards")
        return 1


def cmd_review(args) -> int:
    """Start a review session."""
    try:
        # Set up signal handlers for graceful interruption
        setup_signal_handlers()

        # Create review session
        session = create_review_session(args.deck)
        if not session:
            print_error(f"Deck '{args.deck}' not found")
            print_info("Use 'srs list-decks' to see available decks")
            return 1

        if not session.has_cards:
            print_header(f"Review: {args.deck}")
            print_info("No cards due for review")
            print("All caught up! 🎉")
            return 0

        # Apply limit if specified
        if args.limit and args.limit < session.stats.total_cards:
            # Limit the cards in the session
            limited_cards = session.cards[:args.limit]
            session = create_review_session(args.deck, limited_cards)

        # Show session start
        print_header(f"Review Session: {session.deck.name}")
        print(f"📚 Deck: {session.deck.name}")
        print(f"📊 Cards to review: {session.stats.total_cards}")
        if args.limit:
            print(f"🔢 Limited to: {args.limit} cards")
        print()
        print_info("Press Ctrl+C at any time to stop the session")
        print()
        input("Press ENTER to start reviewing...")

        # Review loop
        try:
            while session.has_cards:
                card = session.current_card
                progress = session.progress

                # Clear screen and show progress
                print("\n" * 2)
                print_separator(70)
                print(f"📚 {session.deck.name} | Card {progress['current_position']} of {progress['total_cards']} | {progress['completion_percentage']:.0f}% complete")
                print_separator(70)
                print()

                # Show question
                print("❓ Question:")
                print(f"   {card.front}")
                print()

                # Wait for user to see answer
                try:
                    input("💡 Press ENTER to reveal answer...")
                except (EOFError, KeyboardInterrupt):
                    raise KeyboardInterrupt

                print()
                print("✅ Answer:")
                print(f"   {card.back}")
                print()
                print_separator(50)

                # Get rating
                while True:
                    print("🎯 How well did you know this?")
                    print()
                    print("  1️⃣  Again     - Completely forgot")
                    print("  2️⃣  Hard      - Struggled to recall")
                    print("  3️⃣  Good      - Recalled with effort")
                    print("  4️⃣  Easy      - Instant recall")
                    print()

                    try:
                        choice = input("Your choice [1-4]: ").strip()
                        if choice in ['1', '2', '3', '4']:
                            rating = int(choice)
                            break
                        elif choice.lower() in ['q', 'quit', 'exit']:
                            raise KeyboardInterrupt
                        else:
                            print_warning("Please enter 1, 2, 3, or 4 (or 'q' to quit)")
                            print()
                    except (ValueError, EOFError, KeyboardInterrupt):
                        raise KeyboardInterrupt

                # Review the card
                session.review_current_card(rating)

                # Show brief feedback
                rating_feedback = {
                    1: "📚 Card will be shown again soon",
                    2: "⏰ Card scheduled for review later",
                    3: "✅ Good! Card scheduled appropriately",
                    4: "🚀 Easy! Card scheduled for later"
                }
                print()
                print(rating_feedback[rating])

                # Brief pause before next card
                if session.has_cards:
                    print()
                    input("Press ENTER for next card...")

        except KeyboardInterrupt:
            print("\n")
            print_warning("Review session interrupted")

            # Ask if user wants to see partial summary
            if session.stats.cards_reviewed > 0:
                if confirm_action("Show session summary?", default=True):
                    show_session_summary(session)
            else:
                print_info("No cards were reviewed")
            return 0

        # Show session summary
        print("\n")
        print_header("🎉 Review Session Complete!")
        show_session_summary(session)

        return 0

    except Exception as e:
        logger.error(f"Error in review session: {e}")
        print_error("Review session failed")
        return 1


def show_session_summary(session):
    """Display a formatted session summary."""
    summary = session.get_session_summary()

    print(f"📊 Cards reviewed: {summary['cards_reviewed']}")
    print(f"⏱️  Duration: {format_duration(summary['duration_minutes'] * 60)}")
    print(f"📈 Average rating: {summary['average_rating']:.1f}")
    print()

    # Rating breakdown with visual indicators
    print("📋 Rating breakdown:")
    rating_info = {
        1: ("Again", "🔴"),
        2: ("Hard", "🟡"),
        3: ("Good", "🟢"),
        4: ("Easy", "🔵")
    }

    for rating, count in summary['rating_counts'].items():
        if count > 0:
            name, emoji = rating_info[rating]
            percentage = (count / summary['cards_reviewed']) * 100
            print(f"  {emoji} {name}: {count} ({percentage:.0f}%)")

    # Encouragement message
    if summary['average_rating'] >= 3.5:
        print("\n🌟 Excellent work! You're mastering these cards!")
    elif summary['average_rating'] >= 2.5:
        print("\n👍 Good progress! Keep up the practice!")
    else:
        print("\n💪 Keep practicing! You'll improve with time!")


def cmd_status(args) -> int:
    """Show system status."""
    try:
        decks = Deck.get_all()

        if not decks:
            print_header("📊 SRS Status")
            print_info("No decks found")
            print("Get started by creating your first deck:")
            print("  srs create-deck \"My First Deck\"")
            return 0

        print_header("📊 SRS Status")

        total_due = 0
        total_new = 0
        total_cards = 0
        decks_with_due = []

        # Collect deck information
        for deck in decks:
            counts = deck.get_card_counts()
            total_due += counts['due']
            total_new += counts['new']
            total_cards += counts['total']

            if counts['due'] > 0:
                decks_with_due.append((deck, counts))

        # Show overview
        print(f"📚 Total decks: {len(decks)}")
        print(f"📊 Total cards: {total_cards}")
        print(f"🔥 Cards due: {total_due}")
        print(f"🆕 New cards: {total_new}")
        print()

        # Show decks with due cards first
        if decks_with_due:
            print_separator()
            print("🔥 Decks with cards due for review:")
            print()

            for deck, counts in decks_with_due:
                status_icon = "🔥"
                print(f"  {status_icon} {deck.name:<25} {counts['due']} due, {counts['new']} new")

                if args.detailed:
                    due_cards = Card.get_due_cards(deck.id)
                    if due_cards:
                        oldest_due = min(card.due_date for card in due_cards)
                        print(f"     Oldest due: {oldest_due.strftime('%Y-%m-%d %H:%M')}")
            print()

        # Show all decks if detailed or if no due cards
        if args.detailed or not decks_with_due:
            print_separator()
            print("📚 All decks:")
            print()

            for i, deck in enumerate(decks, 1):
                counts = deck.get_card_counts()
                count_str = format_card_counts(counts)
                status_icon = "🔥" if counts['due'] > 0 else "📚"

                print(f"{i:2d}. {status_icon} {deck.name:<25} {count_str}")

                if args.detailed:
                    print(f"     Created: {deck.created_at.strftime('%Y-%m-%d %H:%M')}")
                    if counts['total'] > 0:
                        print(f"     Total: {counts['total']}, Due: {counts['due']}, New: {counts['new']}")
                    print()

        # Show recommendations
        print_separator()
        if total_due > 0:
            print_info(f"You have {total_due} cards ready for review!")
            print("Start reviewing with:")
            for deck, counts in decks_with_due[:3]:  # Show top 3
                print(f"  srs review \"{deck.name}\"")
            if len(decks_with_due) > 3:
                print(f"  ... and {len(decks_with_due) - 3} more decks")
        elif total_new > 0:
            print_info(f"You have {total_new} new cards to learn!")
            print("Start learning with:")
            print("  srs review \"<deck-name>\"")
        else:
            print_success("All caught up! 🎉")
            print("Add more cards or wait for scheduled reviews.")

        return 0

    except Exception as e:
        logger.error(f"Error showing status: {e}")
        print_error("Failed to show status")
        return 1


def cli(argv=None):
    """Main CLI entry point."""
    # Set up signal handlers for graceful shutdown
    setup_signal_handlers()

    parser = setup_argument_parser()
    args = parser.parse_args(argv)

    # Initialize the application
    try:
        initialize_srs(args.config if hasattr(args, 'config') else None)

        # Set log level based on verbose flag
        if hasattr(args, 'verbose') and args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)

    except Exception as e:
        print_error(f"Failed to initialize SRS: {e}")
        return 1

    # Handle no command
    if not args.command:
        print_header("🧠 Spaced Repetition System")
        print("A terminal-based flashcard application for efficient learning.")
        print()
        parser.print_help()
        print()
        print_info("Start by creating your first deck:")
        print("  srs create-deck \"My First Deck\"")
        return 0

    # Dispatch to command handlers
    command_handlers = {
        'create-deck': cmd_create_deck,
        'list-decks': cmd_list_decks,
        'delete-deck': cmd_delete_deck,
        'add-card': cmd_add_card,
        'import-cards': cmd_import_cards,
        'list-cards': cmd_list_cards,
        'review': cmd_review,
        'status': cmd_status,
    }

    handler = command_handlers.get(args.command)
    if handler:
        try:
            return handler(args)
        except KeyboardInterrupt:
            print("\n")
            print_info("Operation cancelled by user")
            return 1
        except Exception as e:
            logger.error(f"Unexpected error in command {args.command}: {e}")
            print_error(f"Command '{args.command}' failed")
            return 1
    else:
        print_error(f"Unknown command '{args.command}'")
        print_info("Use 'srs --help' to see available commands")
        return 1


if __name__ == '__main__':
    import sys
    sys.exit(cli())
