[project]
name = "spaced-repetition-cli"
version = "0.1.0"
description = ""
authors = [
    {name = "jachian22",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.13,<3.14"
dependencies = [
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.dev.dependencies]
black = ">=24.6.0"
ruff = ">=0.4.0"
pytest = ">=8.1.0"
pytest-cov = "^6.2.1"

[tool.poetry]
# …existing keys…
packages = [{ include = "srs" }]

[tool.poetry.scripts]
srs = "srs.cli:cli"
