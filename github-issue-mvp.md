# Implement MVP: Terminal-Based Spaced Repetition System

## Overview

Implement the foundational MVP for a spaced repetition learning system as outlined in the [Product Requirements Document](./spaced-repetition-prd.md). This MVP will provide a fully functional terminal-based application implementing the SM-2 algorithm with SQLite persistence, serving as the foundation for future phases.

**Target Timeline:** 2-3 weeks  
**Priority:** High (foundational feature)

## Core Features

### 1. Database Schema & Persistence
Implement SQLite database with the following schema:

<details>
<summary>Database Schema (Click to expand)</summary>

```sql
-- Core tables for MVP
CREATE TABLE decks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE cards (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    deck_id INTEGER NOT NULL,
    front TEXT NOT NULL,
    back TEXT NOT NULL,
    interval INTEGER DEFAULT 0,
    repetitions INTEGER DEFAULT 0,
    ease_factor REAL DEFAULT 2.5,
    due_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (deck_id) REFERENCES decks(id)
);

CREATE TABLE reviews (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    card_id INTEGER NOT NULL,
    rating INTEGER NOT NULL,
    reviewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (card_id) REFERENCES cards(id)
);

-- Performance optimization
CREATE INDEX idx_due_cards ON cards(due_date, deck_id);
```
</details>

### 2. Terminal Commands Interface

| Command | Description | Example |
|---------|-------------|---------|
| `srs create-deck <name>` | Create a new deck | `srs create-deck "Spanish Verbs"` |
| `srs list-decks` | Show all decks with card counts | `srs list-decks` |
| `srs delete-deck <name>` | Remove deck and all cards | `srs delete-deck "Spanish Verbs"` |
| `srs add-card <deck>` | Interactive prompt for front/back | `srs add-card "Spanish Verbs"` |
| `srs import-cards <deck> <file>` | Bulk import from CSV/TSV | `srs import-cards "Spanish Verbs" cards.csv` |
| `srs list-cards <deck>` | Show all cards in deck | `srs list-cards "Spanish Verbs"` |
| `srs review <deck>` | Start review session | `srs review "Spanish Verbs"` |
| `srs status` | Show due cards across all decks | `srs status` |

### 3. Review Interface
Implement the terminal-based review flow as specified in the PRD:

```
[Deck: Spanish Verbs] [Card 1 of 15]

Q: comer

[Press ENTER to show answer]

A: to eat

How well did you know this?
1) Again - Completely forgot
2) Hard - Struggled to recall  
3) Good - Recalled with effort
4) Easy - Instant recall

Your choice [1-4]: _
```

### 4. SM-2 Algorithm Implementation
- **Learning steps:** 1 min, 10 min (for new cards)
- **Minimum interval:** 1 day
- **Maximum interval:** 365 days
- **Easy bonus:** 1.3x multiplier
- **Interval fuzz:** ±5% to prevent clustering
- **Ease factor adjustments** based on review ratings

## Technical Requirements

- **Database:** SQLite for simplicity and portability
- **Dependencies:** Standard library only (no external dependencies beyond dev tools)
- **Configuration:** Support for environment variables or `.srsrc` file
- **Architecture:** Single executable/script
- **Python Version:** >=3.13,<3.14 (as specified in pyproject.toml)
- **Package Management:** Poetry (existing setup)

## Acceptance Criteria

### Database & Persistence
- [ ] SQLite database is created automatically on first run
- [ ] All tables and indexes are created correctly
- [ ] Data persists between application sessions
- [ ] Database handles concurrent access safely
- [ ] Proper foreign key constraints are enforced

### Deck Management
- [ ] Can create decks with unique names
- [ ] Can list all decks with card counts (total, due, new)
- [ ] Can delete decks and all associated cards
- [ ] Deck names are validated (non-empty, reasonable length)
- [ ] Appropriate error messages for duplicate/missing decks

### Card Management
- [ ] Can add cards interactively with front/back text
- [ ] Can import cards from CSV/TSV files with proper format validation
- [ ] Can list all cards in a deck with basic information
- [ ] Card content supports Unicode characters
- [ ] Import handles common CSV edge cases (quotes, commas, newlines)

### Review System
- [ ] Review sessions show cards in correct order (due cards first)
- [ ] Terminal interface matches the specified design
- [ ] All four rating options (1-4) work correctly
- [ ] Review sessions can be interrupted and resumed
- [ ] Progress indicator shows current position in session

### SM-2 Algorithm
- [ ] New cards follow learning steps (1 min, 10 min)
- [ ] Graduated cards use SM-2 intervals correctly
- [ ] Ease factor adjustments work for all rating types
- [ ] Interval calculations respect min/max bounds
- [ ] Fuzz factor is applied to prevent clustering
- [ ] Due dates are calculated accurately

### CLI Interface
- [ ] All commands work as specified in the command table
- [ ] Help text is available for all commands
- [ ] Error messages are clear and actionable
- [ ] Command-line argument parsing is robust
- [ ] Exit codes are appropriate (0 for success, non-zero for errors)

### Performance & Reliability
- [ ] Review of 100 cards completes in under 5 minutes
- [ ] Database operations complete in under 100ms
- [ ] No data loss during normal operation
- [ ] Graceful handling of corrupted database files
- [ ] Memory usage remains reasonable for large decks

## Implementation Details

### Project Structure
```
srs/
├── __init__.py
├── cli.py              # Command-line interface (expand existing)
├── database.py         # SQLite operations and schema
├── models.py           # Data models (Deck, Card, Review)
├── algorithm.py        # SM-2 implementation
├── review.py           # Review session logic
└── utils.py            # Helper functions
```

### Configuration
- Default database location: `~/.srs/data.db`
- Configuration file: `~/.srsrc` (optional)
- Environment variables: `SRS_DATABASE_PATH`, `SRS_CONFIG_PATH`

### Error Handling
- Graceful degradation for missing files
- Clear error messages for user mistakes
- Automatic database repair for minor corruption
- Proper logging for debugging

## Success Metrics

- [ ] All acceptance criteria are met
- [ ] Test coverage ≥95% for algorithm code
- [ ] Zero crashes during 1000 card reviews
- [ ] Accurate interval calculations (verified against known test cases)
- [ ] Documentation covers all commands and features

## Resources

- **Full Specification:** [spaced-repetition-prd.md](./spaced-repetition-prd.md)
- **SM-2 Algorithm:** [Original Paper](https://www.supermemo.com/en/archives1990-2015/english/ol/sm2)
- **Current Codebase:** Minimal CLI placeholder in `srs/cli.py`

## Notes

This MVP serves as the foundation for all future phases. Focus on:
1. **Correctness** of the SM-2 algorithm implementation
2. **Data integrity** and persistence
3. **User experience** in the terminal interface
4. **Code quality** for future extensibility

Future phases will add web interface, cloud sync, and advanced features, but this MVP must be rock-solid as the core foundation.

---

**Definition of Done:** All acceptance criteria are met, tests pass, and the system can successfully manage decks, cards, and review sessions with accurate spaced repetition scheduling.
