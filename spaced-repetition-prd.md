# Product Requirements Document: Spaced Repetition Learning System

## Executive Summary

This PRD outlines the development of a spaced repetition learning system that abstracts away algorithmic complexity while maintaining proven effectiveness. The system will evolve from a single-user terminal application to a full-featured learning platform through carefully planned phases.

---

## MVP (Phase 0): Terminal-Based Single User System

### Overview
A functional spaced repetition system accessible via terminal commands, implementing core SM-2 algorithm with essential database persistence.

### Core Features

#### 1. Database Schema
```sql
-- Minimal viable schema
CREATE TABLE decks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE cards (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    deck_id INTEGER NOT NULL,
    front TEXT NOT NULL,
    back TEXT NOT NULL,
    interval INTEGER DEFAULT 0,
    repetitions INTEGER DEFAULT 0,
    ease_factor REAL DEFAULT 2.5,
    due_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREI<PERSON><PERSON> KEY (deck_id) REFERENCES decks(id)
);

CREATE TABLE reviews (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    card_id INTEGER NOT NULL,
    rating INTEGER NOT NULL,
    reviewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (card_id) REFERENCES cards(id)
);

-- Essential index for performance
CREATE INDEX idx_due_cards ON cards(due_date, deck_id);
```

#### 2. Terminal Commands

**Deck Management:**
- `srs create-deck <name>` - Create a new deck
- `srs list-decks` - Show all decks with card counts
- `srs delete-deck <name>` - Remove deck and all cards

**Card Management:**
- `srs add-card <deck>` - Interactive prompt for front/back
- `srs import-cards <deck> <file>` - Bulk import from CSV/TSV
- `srs list-cards <deck>` - Show all cards in deck

**Review Session:**
- `srs review <deck>` - Start review session
- `srs status` - Show due cards across all decks

#### 3. Review Interface
```
Terminal Review Flow:
==================
[Deck: Spanish Verbs] [Card 1 of 15]

Q: comer

[Press ENTER to show answer]

A: to eat

How well did you know this?
1) Again - Completely forgot
2) Hard - Struggled to recall  
3) Good - Recalled with effort
4) Easy - Instant recall

Your choice [1-4]: _
```

#### 4. Core Algorithm Implementation
- SM-2 algorithm for scheduling
- Fixed learning steps: 1 min, 10 min (for new cards)
- Minimum interval: 1 day
- Maximum interval: 365 days
- Easy bonus: 1.3x
- Interval fuzz: ±5% to prevent clustering

### Technical Requirements
- SQLite database for simplicity
- Single executable/script
- No external dependencies beyond standard library
- Configuration via environment variables or `.srsrc` file

### Success Criteria
- Can create decks and add cards
- Review sessions correctly apply SM-2 algorithm
- Due dates update appropriately after reviews
- Data persists between sessions

---

## Phase 1: Enhanced Terminal Experience

### New Features

#### 1. Statistics and Progress
- `srs stats [deck]` - Show retention rates, daily reviews
- Progress indicators during review sessions
- Streak tracking for motivation

#### 2. Smart Scheduling
- Daily review limits
- Time-boxed sessions (e.g., 20 minutes)
- New card graduation settings

#### 3. Better Import/Export
- Anki-compatible import
- Markdown format support
- Export with scheduling data

#### 4. Search and Filter
- `srs search <query>` - Find cards by content
- `srs review --hard` - Review only difficult cards
- Tag system for organization

### Database Additions
```sql
CREATE TABLE tags (
    id INTEGER PRIMARY KEY,
    name TEXT UNIQUE NOT NULL
);

CREATE TABLE card_tags (
    card_id INTEGER,
    tag_id INTEGER,
    PRIMARY KEY (card_id, tag_id)
);

ALTER TABLE cards ADD COLUMN difficulty REAL DEFAULT 0.5;
ALTER TABLE decks ADD COLUMN daily_new_limit INTEGER DEFAULT 20;
ALTER TABLE decks ADD COLUMN daily_review_limit INTEGER DEFAULT 200;
```

---

## Phase 2: Web Interface & Supabase Migration

### Overview
This phase marks a critical architectural transition from local SQLite to Supabase, enabling multi-user support, real-time features, and robust authentication while maintaining backwards compatibility for single-user mode.

### Migration to Supabase

#### Why Supabase?
- **Built-in Authentication**: Email/password, OAuth providers, magic links
- **Row Level Security (RLS)**: User data isolation at database level
- **Real-time Subscriptions**: Live sync across devices
- **Auto-generated APIs**: Instant REST and GraphQL endpoints
- **Edge Functions**: Server-side logic for complex operations
- **Vector embeddings**: Future ML/AI features (Phase 5)

#### Migration Strategy
1. **Dual Mode Operation**: 
   - Local SQLite mode remains available via `--local` flag
   - Cloud mode becomes default for new users
   
2. **One-time Migration Tool**:
   ```bash
   $ srs migrate-to-cloud
   Creating Supabase account...
   Uploading 1,234 cards...
   Migration complete! Use 'srs login' to continue.
   ```

3. **Schema Translation**:
   ```sql
   -- Supabase schema with RLS
   CREATE TABLE profiles (
     id UUID REFERENCES auth.users ON DELETE CASCADE,
     username TEXT UNIQUE,
     created_at TIMESTAMPTZ DEFAULT NOW(),
     PRIMARY KEY (id)
   );
   
   CREATE TABLE decks (
     id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
     user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
     name TEXT NOT NULL,
     daily_new_limit INTEGER DEFAULT 20,
     daily_review_limit INTEGER DEFAULT 200,
     created_at TIMESTAMPTZ DEFAULT NOW(),
     updated_at TIMESTAMPTZ DEFAULT NOW()
   );
   
   CREATE TABLE cards (
     id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
     deck_id UUID REFERENCES decks(id) ON DELETE CASCADE,
     front TEXT NOT NULL,
     back TEXT NOT NULL,
     interval INTEGER DEFAULT 0,
     repetitions INTEGER DEFAULT 0,
     ease_factor REAL DEFAULT 2.5,
     due_date TIMESTAMPTZ DEFAULT NOW(),
     created_at TIMESTAMPTZ DEFAULT NOW(),
     updated_at TIMESTAMPTZ DEFAULT NOW()
   );
   
   -- Enable RLS
   ALTER TABLE decks ENABLE ROW LEVEL SECURITY;
   ALTER TABLE cards ENABLE ROW LEVEL SECURITY;
   
   -- RLS Policies
   CREATE POLICY "Users can only see own decks" ON decks
     FOR ALL USING (auth.uid() = user_id);
   
   CREATE POLICY "Users can only see own cards" ON cards
     FOR ALL USING (
       deck_id IN (SELECT id FROM decks WHERE user_id = auth.uid())
     );
   ```

### New Features

#### 1. Authentication & User Management
- Email/password registration
- OAuth (Google, GitHub)
- Password reset flow
- Profile management

#### 2. Modern Review Interface
- React-based SPA
- Keyboard shortcuts (spacebar to flip, 1-4 for ratings)
- Card preview in deck view
- Markdown rendering support
- Rich text editor for card creation

#### 3. Real-time Sync
- Live updates across browser tabs
- Conflict-free review merging
- Offline queue with background sync

#### 4. Visual Analytics Dashboard
- Heatmap calendar of reviews
- Forecast graph for upcoming reviews
- Performance trends
- Deck statistics

### Technical Architecture
```
┌─────────────┐ ┌─────────────┐
│  Web Client │ │Terminal CLI │
│   (React)   │ │  (--local)  │
└──────┬──────┘ └──────┬──────┘
       │               │
       │         ┌─────▼─────┐
       │         │  SQLite   │
       │         │  (local)  │
       │         └───────────┘
       │
┌──────▼──────────┐
│   Supabase      │
├─────────────────┤
│ • Auth          │
│ • Database      │
│ • Realtime      │
│ • Edge Functions│
└─────────────────┘
```

### Implementation Details

#### Environment Configuration
```bash
# .env for cloud mode
SUPABASE_URL=https://xxx.supabase.co
SUPABASE_ANON_KEY=xxx
SUPABASE_SERVICE_KEY=xxx  # Server-side only

# Auto-detection
$ srs review  # Uses cloud if .env exists
$ srs review --local  # Forces SQLite mode
```

#### Edge Functions for Complex Operations
```typescript
// supabase/functions/process-review/index.ts
serve(async (req) => {
  const { cardId, rating } = await req.json()
  
  // Apply SM-2 algorithm
  // Update card with new interval
  // Log review for analytics
  // Return next card
})
```

---

## Phase 3: Algorithm Advancement

### New Features

#### 1. FSRS Implementation
- Automatic migration from SM-2
- A/B testing framework
- Personalized parameters

#### 2. Intelligent Scheduling
- Contextual intervals based on card type
- Automatic ease adjustment
- Sibling card handling

#### 3. Learning Modes
- Cramming mode for exams
- Long-term retention mode
- Custom scheduling profiles

### Technical Requirements
- Background scheduler process
- Algorithm performance metrics
- Data export for analysis

---

## Phase 4: Enhanced Collaboration & Sync

### New Features

#### 1. Advanced Multi-Device Sync
- Supabase Realtime subscriptions for instant updates
- Smart conflict resolution for simultaneous reviews
- Bandwidth-optimized delta sync
- Queue management for offline changes

#### 2. Shared Decks Marketplace
- Public deck repository with search
- Deck forking and versioning
- Subscription to deck updates
- Creator analytics and feedback
- Moderation and quality ratings

#### 3. Study Groups & Social Features
- Create/join study groups
- Shared progress dashboards
- Group challenges and goals
- Discussion threads per deck
- Optional competitive leaderboards

### Supabase Schema Additions
```sql
-- Shared decks functionality
CREATE TABLE published_decks (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    original_deck_id UUID REFERENCES decks(id),
    publisher_id UUID REFERENCES profiles(id),
    title TEXT NOT NULL,
    description TEXT,
    tags TEXT[],
    version INTEGER DEFAULT 1,
    fork_count INTEGER DEFAULT 0,
    subscriber_count INTEGER DEFAULT 0,
    published_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Study groups
CREATE TABLE study_groups (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    created_by UUID REFERENCES profiles(id),
    is_public BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE group_members (
    group_id UUID REFERENCES study_groups(id),
    user_id UUID REFERENCES profiles(id),
    role TEXT DEFAULT 'member',
    joined_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (group_id, user_id)
);

-- Enhanced sync tracking
CREATE TABLE sync_queue (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id),
    device_id TEXT NOT NULL,
    operation JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ
);

-- Enable RLS on new tables
ALTER TABLE published_decks ENABLE ROW LEVEL SECURITY;
ALTER TABLE study_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_members ENABLE ROW LEVEL SECURITY;
```

### Leveraging Supabase Features

#### Real-time Collaboration
```typescript
// Subscribe to group updates
const groupChannel = supabase.channel('group:123')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'group_members'
  }, handleMemberUpdate)
  .subscribe()
```

#### Edge Function for Smart Sync
```typescript
// supabase/functions/sync-reviews/index.ts
// Handles conflict resolution when multiple devices review same cards
serve(async (req) => {
  const { reviews, deviceId, lastSyncTime } = await req.json()
  
  // Get server-side reviews since last sync
  // Merge using "most conservative" rule
  // Return unified state
})
```

---

## Phase 5: AI Enhancement with Supabase

### Overview
Leverage Supabase's advanced features (pgvector, Edge Functions, Storage) to build intelligent learning assistance without managing separate AI infrastructure.

### New Features

#### 1. Smart Card Generation
- **Text-to-cards**: Auto-generate from documents/notes
- **Image occlusion**: Visual learning with Storage CDN
- **Cloze deletion**: Intelligent gap identification
- **Multi-modal cards**: Audio, images, diagrams

#### 2. Semantic Search & Discovery
- **Vector embeddings**: Find conceptually similar cards
- **Smart deck recommendations**: Based on learning patterns
- **Knowledge graph**: Visualize concept relationships
- **Duplicate detection**: Prevent redundant cards

#### 3. Personalized Learning Intelligence
- **Difficulty prediction**: ML-based initial intervals
- **Adaptive hints**: Context-aware assistance
- **Optimal review timing**: Circadian-aware scheduling
- **Knowledge gap analysis**: Identify weak areas

### Supabase Implementation

#### Vector Search Setup
```sql
-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Add embeddings to cards
ALTER TABLE cards ADD COLUMN embedding vector(1536);

-- Create similarity search function
CREATE OR REPLACE FUNCTION search_similar_cards(
  query_embedding vector(1536),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id uuid,
  front text,
  back text,
  similarity float
)
LANGUAGE sql STABLE
AS $
  SELECT
    id,
    front,
    back,
    1 - (cards.embedding <=> query_embedding) as similarity
  FROM cards
  WHERE 1 - (cards.embedding <=> query_embedding) > match_threshold
  ORDER BY cards.embedding <=> query_embedding
  LIMIT match_count;
$;
```

#### Edge Functions for AI
```typescript
// supabase/functions/generate-cards/index.ts
serve(async (req) => {
  const { content, deckId } = await req.json()
  
  // Call OpenAI to generate cards
  const cards = await generateCardsFromText(content)
  
  // Generate embeddings
  const embeddings = await generateEmbeddings(cards)
  
  // Store in database with vectors
  await supabase.from('cards').insert(
    cards.map((card, i) => ({
      ...card,
      deck_id: deckId,
      embedding: embeddings[i]
    }))
  )
})
```

#### Storage Integration
```typescript
// Image occlusion with Supabase Storage
const { data, error } = await supabase.storage
  .from('card-images')
  .upload(`${userId}/${cardId}/base.png`, imageFile)

// Serve via CDN
const imageUrl = supabase.storage
  .from('card-images')
  .getPublicUrl(`${userId}/${cardId}/base.png`)
```

---

## Implementation Priorities

### MVP Must-Haves
1. ✅ Reliable SM-2 implementation
2. ✅ Data persistence
3. ✅ Basic review loop
4. ✅ Terminal interface

### Quality Standards
- 95%+ test coverage for algorithm code
- Sub-100ms response time for all operations
- Zero data loss tolerance
- Backwards compatibility between versions

### Performance Targets
- Support 100,000+ cards per user
- Review 100 cards in under 5 minutes
- Instant (<50ms) card retrieval
- Batch operations for large imports

---

## User Stories by Phase

### MVP User Stories
- As a learner, I can create a deck and add cards so I can start learning
- As a learner, I can review due cards with spaced intervals so I retain information
- As a learner, I can see how many cards are due so I can plan study time

### Phase 1 User Stories
- As a learner, I can see my learning statistics so I understand my progress
- As a learner, I can import existing study materials so I don't start from scratch
- As a learner, I can limit daily reviews so I don't get overwhelmed

### Phase 2 User Stories
- As a learner, I can use a web interface so I have a better study experience
- As a learner, I can use keyboard shortcuts so I can review efficiently
- As a learner, I can see visual progress so I stay motivated

---

## Technical Architecture

### MVP Architecture (Phase 0-1)
```
┌─────────────┐
│   Terminal  │
│     CLI     │
└──────┬──────┘
       │
┌──────▼──────┐
│  Core Logic │
│    (SM-2)   │
└──────┬──────┘
       │
┌──────▼──────┐
│   SQLite    │
│  (local DB) │
└─────────────┘
```

### Phase 2 Architecture (Supabase Migration)
```
┌─────────────┐ ┌─────────────┐
│  Web Client │ │Terminal CLI │
│   (React)   │ │             │
└──────┬──────┘ └──────┬──────┘
       │               │
       ├───────────────┤
       │               │
┌──────▼──────────┐    │
│   Supabase      │    │
├─────────────────┤    │
│ • PostgreSQL    │    │
│ • Auth          │    │
│ • Realtime      │    │
│ • REST API      │    │
│ • Edge Functions│    │
└─────────────────┘    │
                       │
                  ┌────▼────┐
                  │ SQLite  │
                  │ (--local)│
                  └─────────┘
```

### Target Architecture (Phase 5)
```
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│  Web Client │ │Mobile Client│ │Terminal CLI │
└──────┬──────┘ └──────┬──────┘ └──────┬──────┘
       │               │               │
       └───────────────┴───────────────┘
                       │
         ┌─────────────▼─────────────┐
         │      Supabase Cloud       │
         ├───────────────────────────┤
         │ • PostgreSQL + pgvector   │
         │ • Auth + OAuth providers  │
         │ • Realtime subscriptions  │
         │ • Edge Functions (API)    │
         │ • Storage (media)         │
         │ • Vector embeddings (AI)  │
         └───────────────────────────┘
```

### Benefits of Supabase Migration

#### Immediate Benefits (Phase 2)
- **Zero Auth Implementation**: Complete auth system out-of-box
- **Instant API**: Auto-generated REST endpoints for all tables
- **Security by Default**: Row Level Security policies
- **Real-time Updates**: WebSocket subscriptions included

#### Scalability Benefits (Phase 3-4)
- **Horizontal Scaling**: Handles millions of users automatically
- **Global Edge Network**: Low latency worldwide
- **Connection Pooling**: Efficient database connections
- **Built-in Caching**: Automatic query optimization

#### Future-Proofing (Phase 5)
- **Vector Search**: pgvector for AI-powered features
- **Edge Computing**: Functions run close to users
- **Media Storage**: Card images, audio, attachments
- **Webhooks**: Third-party integrations

### Migration Path Safeguards

#### Data Portability
- Export to standard formats (Anki, CSV, JSON)
- Local backup command: `srs backup --local`
- Full data download via Supabase dashboard

#### Graceful Degradation
```bash
# Automatic fallback
$ srs review  # Tries cloud, falls back to local if offline

# Explicit modes
$ srs review --cloud  # Force cloud mode
$ srs review --local  # Force local mode

# Sync when ready
$ srs sync  # Push local changes to cloud when online
```

#### Cost Considerations
- Free tier supports up to 10,000 users
- ~$0.01 per user per month at scale
- Edge functions: 2M invocations free
- Storage: 1GB free, $0.021/GB after

---

## Success Metrics

### MVP Metrics
- Successful implementation of SM-2 algorithm
- Zero crashes during 1000 card reviews
- Accurate interval calculations

### Growth Metrics (by Phase 3)
- 90% of cards reviewed within 1 day of due date
- Average session time > 5 minutes
- 80% user retention after 30 days

### Long-term Metrics (by Phase 5)
- 95% algorithm accuracy (predicted vs actual recall)
- < 2% sync conflicts
- 50% of users using shared decks

---

## Risk Mitigation

### Technical Risks
- **Algorithm bugs**: Extensive unit testing, known test cases
- **Data loss**: Automated backups, transaction safety
- **Performance degradation**: Profiling, index optimization
- **Supabase vendor lock-in**: Data portability, local mode retention

### User Experience Risks
- **Complexity creep**: User testing at each phase
- **Migration friction**: Automated migration tools, dual-mode support
- **Feature overload**: Progressive disclosure, sensible defaults

### Supabase-Specific Considerations
- **Free tier limits**: Monitor usage, implement caching
- **Network dependency**: Robust offline mode, sync queue
- **Migration failures**: Incremental migration, rollback capability

---

## Supabase Feature Utilization Roadmap

### Phase 2 (Foundation)
- **Auth**: Email/password, magic links
- **Database**: Basic tables with RLS
- **Auto-generated REST API**: CRUD operations

### Phase 3 (Enhancement)
- **Edge Functions**: Algorithm processing
- **Database Functions**: Complex queries
- **Realtime**: Live review sessions

### Phase 4 (Collaboration)
- **Realtime Broadcast**: Group study sessions
- **Database Webhooks**: Deck update notifications
- **Storage**: Profile pictures, deck thumbnails

### Phase 5 (AI/ML)
- **pgvector**: Semantic card search
- **Edge Functions**: AI card generation
- **Storage CDN**: Media attachments
- **Database Extensions**: Advanced analytics

---

## Development Timeline

### MVP: 2-3 weeks
- Week 1: Database, core algorithm
- Week 2: CLI interface, review loop
- Week 3: Testing, polish, documentation

### Phase 1: 3-4 weeks
- Weeks 1-2: Statistics, progress tracking
- Week 3: Import/export, search functionality
- Week 4: Testing, performance optimization

### Phase 2: 8-10 weeks (Includes Supabase Migration)
- Weeks 1-2: Supabase setup, schema migration
- Weeks 3-4: Auth implementation, RLS policies
- Weeks 5-6: React web interface
- Weeks 7-8: Real-time sync, Edge functions
- Weeks 9-10: Migration tools, dual-mode support

### Phase 3: 4-6 weeks
- Weeks 1-3: FSRS implementation
- Weeks 4-6: A/B testing framework, optimization

### Phase 4: 6-8 weeks
- Weeks 1-3: Enhanced sync, conflict resolution
- Weeks 4-5: Shared decks marketplace
- Weeks 6-8: Study groups, social features

### Phase 5: 10-12 weeks
- Weeks 1-4: AI card generation
- Weeks 5-8: ML-based scheduling
- Weeks 9-12: Advanced analytics, insights

**Total: 7-9 months to full platform**

### Supabase Migration Milestones

#### Pre-Migration Checklist
- [ ] Complete test suite for core algorithm
- [ ] Document all SQLite schema constraints
- [ ] Create rollback plan
- [ ] Set up Supabase project and environments

#### Migration Week 1
- [ ] Implement dual-mode architecture
- [ ] Create schema in Supabase
- [ ] Set up RLS policies
- [ ] Test auth flows

#### Migration Week 2  
- [ ] Build migration CLI tool
- [ ] Test with sample datasets
- [ ] Implement sync queue
- [ ] Deploy to staging

#### Post-Migration
- [ ] Monitor performance metrics
- [ ] Gather user feedback on sync
- [ ] Optimize Edge functions
- [ ] Document new architecture

---

## Appendix: Example Commands

### MVP Usage Flow
```bash
# First time setup
$ srs init
Database initialized at ~/.srs/data.db

# Create a deck
$ srs create-deck "Spanish Verbs"
Deck 'Spanish Verbs' created

# Add cards
$ srs add-card "Spanish Verbs"
Front: comer
Back: to eat
Card added. (1 cards in deck)

# Review
$ srs review "Spanish Verbs"
[Review session started...]

# Check status
$ srs status
Spanish Verbs: 5 due, 10 new
French Vocab: 12 due, 0 new
Total: 17 due, 10 new
```