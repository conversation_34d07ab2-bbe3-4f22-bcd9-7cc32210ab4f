# MVP Implementation Plan: Spaced Repetition CLI

This document provides a comprehensive implementation plan for the MVP spaced repetition system based on the GitHub issue specification in `github-issue-mvp.md`.

## Overview

**Target Timeline:** 2-3 weeks  
**Success Criteria:** 95%+ test coverage, all acceptance criteria met  
**Architecture:** Terminal-based CLI with SQLite persistence

## Implementation Phases

### Phase 1: Foundation (Days 1-3)
- Project structure setup
- Database layer implementation
- Core data models

### Phase 2: Core Logic (Days 4-8)
- SM-2 algorithm implementation
- Review session logic
- CLI command framework

### Phase 3: User Interface (Days 9-12)
- Terminal interface implementation
- Command parsing and validation
- Error handling and user feedback

### Phase 4: Integration & Testing (Days 13-15)
- End-to-end integration
- Performance optimization
- Comprehensive testing

---

## Detailed Task Breakdown

### 🏗️ Phase 1: Foundation (3 days)

#### 1.1 Project Structure Setup
**Estimated Time:** 2 hours  
**Dependencies:** None  
**Priority:** High

**Tasks:**
- [ ] Create project directory structure as specified
- [ ] Set up module imports and __init__.py files
- [ ] Configure development environment
- [ ] Set up basic logging framework

**Files to Create:**
```
srs/
├── __init__.py
├── cli.py              # Expand existing placeholder
├── database.py         # New - SQLite operations
├── models.py           # New - Data models
├── algorithm.py        # New - SM-2 implementation
├── review.py           # New - Review session logic
├── utils.py            # New - Helper functions
└── config.py           # New - Configuration management
```

**Test Requirements:**
- [ ] Test module imports work correctly
- [ ] Test logging configuration
- [ ] Test basic project structure

#### 1.2 Database Layer Implementation
**Estimated Time:** 8 hours  
**Dependencies:** 1.1  
**Priority:** High

**Tasks:**
- [ ] Implement database connection management
- [ ] Create schema initialization functions
- [ ] Implement table creation with proper constraints
- [ ] Add database migration support
- [ ] Implement connection pooling and thread safety
- [ ] Add database backup/restore functionality

**Database Schema Implementation:**
```sql
-- Tables: decks, cards, reviews
-- Indexes: idx_due_cards
-- Foreign key constraints
-- Default values and timestamps
```

**Test Requirements:**
- [ ] Test database creation on first run
- [ ] Test schema creation and validation
- [ ] Test foreign key constraints
- [ ] Test concurrent access safety
- [ ] Test database corruption handling
- [ ] Test backup/restore functionality

#### 1.3 Core Data Models
**Estimated Time:** 6 hours  
**Dependencies:** 1.2  
**Priority:** High

**Tasks:**
- [ ] Implement Deck model with CRUD operations
- [ ] Implement Card model with scheduling fields
- [ ] Implement Review model for tracking history
- [ ] Add model validation and constraints
- [ ] Implement model relationships and cascading deletes
- [ ] Add serialization/deserialization methods

**Model Specifications:**
- **Deck:** id, name, created_at, card counts
- **Card:** id, deck_id, front, back, interval, repetitions, ease_factor, due_date
- **Review:** id, card_id, rating, reviewed_at

**Test Requirements:**
- [ ] Test model creation and validation
- [ ] Test CRUD operations for all models
- [ ] Test model relationships and cascading
- [ ] Test data persistence across sessions
- [ ] Test Unicode support in card content
- [ ] Test model serialization

### 🧠 Phase 2: Core Logic (5 days)

#### 2.1 SM-2 Algorithm Implementation
**Estimated Time:** 12 hours  
**Dependencies:** 1.3  
**Priority:** Critical

**Tasks:**
- [ ] Implement core SM-2 algorithm functions
- [ ] Add learning steps for new cards (1 min, 10 min)
- [ ] Implement ease factor calculations
- [ ] Add interval fuzz (±5%) to prevent clustering
- [ ] Implement min/max interval bounds (1 day - 365 days)
- [ ] Add easy bonus multiplier (1.3x)
- [ ] Create algorithm configuration system

**Algorithm Specifications:**
```python
# SM-2 Parameters
INITIAL_EASE_FACTOR = 2.5
MIN_EASE_FACTOR = 1.3
MAX_EASE_FACTOR = 2.5
LEARNING_STEPS = [1, 10]  # minutes
MIN_INTERVAL = 1  # day
MAX_INTERVAL = 365  # days
EASY_BONUS = 1.3
FUZZ_RANGE = 0.05  # ±5%
```

**Test Requirements:**
- [ ] Test new card learning steps progression
- [ ] Test SM-2 interval calculations for all ratings
- [ ] Test ease factor adjustments (1=reset, 2=decrease, 3=maintain, 4=increase)
- [ ] Test interval bounds enforcement
- [ ] Test fuzz factor application
- [ ] Test easy bonus calculation
- [ ] Test edge cases (very low/high ease factors)
- [ ] Test algorithm with known test cases from literature

#### 2.2 Review Session Logic
**Estimated Time:** 10 hours  
**Dependencies:** 2.1  
**Priority:** High

**Tasks:**
- [ ] Implement review queue management
- [ ] Add card ordering logic (due cards first)
- [ ] Implement session state management
- [ ] Add session interruption and resumption
- [ ] Implement progress tracking
- [ ] Add session statistics and reporting

**Review Session Features:**
- Queue due cards by priority
- Track session progress
- Handle user interruptions
- Update card scheduling after reviews
- Maintain session statistics

**Test Requirements:**
- [ ] Test review queue ordering (due cards first)
- [ ] Test session state persistence
- [ ] Test session interruption and resumption
- [ ] Test progress tracking accuracy
- [ ] Test card updates after review
- [ ] Test empty deck handling
- [ ] Test session statistics calculation

#### 2.3 CLI Command Framework
**Estimated Time:** 8 hours  
**Dependencies:** 1.3  
**Priority:** High

**Tasks:**
- [ ] Implement command parser using argparse
- [ ] Add command validation and error handling
- [ ] Implement help system for all commands
- [ ] Add configuration file support (.srsrc)
- [ ] Implement environment variable support
- [ ] Add proper exit codes for all operations

**Command Structure:**
```python
# Main commands to implement
commands = [
    'create-deck', 'list-decks', 'delete-deck',
    'add-card', 'import-cards', 'list-cards',
    'review', 'status'
]
```

**Test Requirements:**
- [ ] Test command parsing for all valid commands
- [ ] Test argument validation
- [ ] Test help text generation
- [ ] Test configuration file loading
- [ ] Test environment variable handling
- [ ] Test error handling and exit codes

### 🖥️ Phase 3: User Interface (4 days)

#### 3.1 Deck Management Commands
**Estimated Time:** 6 hours  
**Dependencies:** 2.3  
**Priority:** High

**Tasks:**
- [ ] Implement `create-deck` command with validation
- [ ] Implement `list-decks` with card count display
- [ ] Implement `delete-deck` with confirmation
- [ ] Add deck name validation (length, characters)
- [ ] Implement duplicate deck handling
- [ ] Add user-friendly error messages

**Command Specifications:**
- `srs create-deck "Deck Name"` - Create new deck
- `srs list-decks` - Show all decks with counts
- `srs delete-deck "Deck Name"` - Delete deck and cards

**Test Requirements:**
- [ ] Test deck creation with valid names
- [ ] Test deck creation with invalid names
- [ ] Test duplicate deck handling
- [ ] Test deck listing with accurate counts
- [ ] Test deck deletion with confirmation
- [ ] Test deck deletion cascading to cards

#### 3.2 Card Management Commands
**Estimated Time:** 8 hours  
**Dependencies:** 3.1  
**Priority:** High

**Tasks:**
- [ ] Implement `add-card` interactive interface
- [ ] Implement `import-cards` CSV/TSV parser
- [ ] Implement `list-cards` with formatting
- [ ] Add Unicode support for card content
- [ ] Handle CSV edge cases (quotes, commas, newlines)
- [ ] Add import validation and error reporting

**Import Format Support:**
```csv
front,back
"comer","to eat"
"hablar","to speak"
```

**Test Requirements:**
- [ ] Test interactive card addition
- [ ] Test CSV import with various formats
- [ ] Test TSV import functionality
- [ ] Test Unicode character support
- [ ] Test CSV edge cases handling
- [ ] Test import error reporting
- [ ] Test card listing and formatting

#### 3.3 Review Interface Implementation
**Estimated Time:** 10 hours
**Dependencies:** 2.2, 3.2
**Priority:** Critical

**Tasks:**
- [ ] Implement terminal review interface
- [ ] Add keyboard input handling
- [ ] Implement progress display
- [ ] Add session interruption handling
- [ ] Implement rating input validation
- [ ] Add review session summary

**Interface Specification:**
```
[Deck: Spanish Verbs] [Card 1 of 15]

Q: comer

[Press ENTER to show answer]

A: to eat

How well did you know this?
1) Again - Completely forgot
2) Hard - Struggled to recall
3) Good - Recalled with effort
4) Easy - Instant recall

Your choice [1-4]: _
```

**Test Requirements:**
- [ ] Test review interface display
- [ ] Test keyboard input handling
- [ ] Test progress indicator accuracy
- [ ] Test rating input validation
- [ ] Test session interruption (Ctrl+C)
- [ ] Test session completion summary
- [ ] Test empty review queue handling

#### 3.4 Status and Utility Commands
**Estimated Time:** 4 hours
**Dependencies:** 3.3
**Priority:** Medium

**Tasks:**
- [ ] Implement `status` command with due card counts
- [ ] Add help command with usage examples
- [ ] Implement version information display
- [ ] Add configuration display command
- [ ] Implement database statistics

**Status Display Format:**
```
Spanish Verbs: 5 due, 10 new
French Vocab: 12 due, 0 new
Total: 17 due, 10 new
```

**Test Requirements:**
- [ ] Test status display accuracy
- [ ] Test help command completeness
- [ ] Test version information
- [ ] Test configuration display
- [ ] Test database statistics

### 🔧 Phase 4: Integration & Testing (3 days)

#### 4.1 End-to-End Integration
**Estimated Time:** 8 hours
**Dependencies:** 3.4
**Priority:** High

**Tasks:**
- [ ] Integrate all components into main CLI
- [ ] Implement proper error propagation
- [ ] Add comprehensive logging
- [ ] Optimize database queries
- [ ] Add performance monitoring
- [ ] Implement graceful shutdown

**Integration Points:**
- CLI → Commands → Models → Database
- Review Session → Algorithm → Database Updates
- Import/Export → File I/O → Database

**Test Requirements:**
- [ ] Test complete user workflows
- [ ] Test error handling across components
- [ ] Test performance under load
- [ ] Test memory usage with large datasets
- [ ] Test graceful shutdown procedures

#### 4.2 Performance Optimization
**Estimated Time:** 6 hours
**Dependencies:** 4.1
**Priority:** Medium

**Tasks:**
- [ ] Optimize database queries with proper indexing
- [ ] Implement query result caching
- [ ] Optimize memory usage for large decks
- [ ] Add database connection pooling
- [ ] Implement batch operations for imports
- [ ] Profile and optimize critical paths

**Performance Targets:**
- Database operations: <100ms
- Review session: 100 cards in <5 minutes
- Memory usage: Reasonable for 100k+ cards

**Test Requirements:**
- [ ] Test database operation performance
- [ ] Test review session performance
- [ ] Test memory usage with large datasets
- [ ] Test import performance with large files
- [ ] Test concurrent access performance

#### 4.3 Comprehensive Testing
**Estimated Time:** 10 hours
**Dependencies:** 4.2
**Priority:** Critical

**Tasks:**
- [ ] Achieve 95%+ test coverage
- [ ] Add integration tests for all workflows
- [ ] Implement stress testing
- [ ] Add regression test suite
- [ ] Test edge cases and error conditions
- [ ] Validate against acceptance criteria

**Test Coverage Requirements:**
- Unit tests: All functions and methods
- Integration tests: Complete user workflows
- Performance tests: Load and stress testing
- Edge case tests: Error conditions and boundaries

**Test Requirements:**
- [ ] Achieve 95%+ code coverage
- [ ] Pass all acceptance criteria tests
- [ ] Complete 1000 card review stress test
- [ ] Validate SM-2 algorithm accuracy
- [ ] Test all error conditions

---

## Comprehensive Test Plan

### 🧪 Test Categories

#### Unit Tests

**Database Layer Tests (database.py)**
```python
class TestDatabase:
    def test_database_creation()
    def test_schema_initialization()
    def test_foreign_key_constraints()
    def test_concurrent_access()
    def test_corruption_handling()
    def test_backup_restore()
```

**Model Tests (models.py)**
```python
class TestDeck:
    def test_deck_creation()
    def test_deck_validation()
    def test_deck_crud_operations()
    def test_deck_card_counts()

class TestCard:
    def test_card_creation()
    def test_card_validation()
    def test_unicode_support()
    def test_scheduling_fields()

class TestReview:
    def test_review_creation()
    def test_review_history()
    def test_review_statistics()
```

**Algorithm Tests (algorithm.py)**
```python
class TestSM2Algorithm:
    def test_new_card_learning_steps()
    def test_interval_calculation_rating_1()
    def test_interval_calculation_rating_2()
    def test_interval_calculation_rating_3()
    def test_interval_calculation_rating_4()
    def test_ease_factor_adjustments()
    def test_interval_bounds()
    def test_fuzz_factor()
    def test_easy_bonus()
    def test_edge_cases()
```

#### Integration Tests

**CLI Integration Tests**
```python
class TestCLIIntegration:
    def test_complete_deck_workflow()
    def test_complete_card_workflow()
    def test_complete_review_workflow()
    def test_import_export_workflow()
    def test_error_handling_workflow()
```

**Review Session Tests**
```python
class TestReviewSession:
    def test_session_creation()
    def test_card_ordering()
    def test_session_interruption()
    def test_session_resumption()
    def test_progress_tracking()
    def test_session_completion()
```

#### Performance Tests

**Load Testing**
```python
class TestPerformance:
    def test_large_deck_performance()
    def test_database_query_performance()
    def test_review_session_performance()
    def test_import_performance()
    def test_memory_usage()
```

#### Acceptance Criteria Tests

**Database & Persistence**
- [ ] test_database_auto_creation()
- [ ] test_schema_creation()
- [ ] test_data_persistence()
- [ ] test_concurrent_access()
- [ ] test_foreign_key_constraints()

**Deck Management**
- [ ] test_create_unique_decks()
- [ ] test_list_decks_with_counts()
- [ ] test_delete_deck_cascade()
- [ ] test_deck_name_validation()
- [ ] test_duplicate_deck_errors()

**Card Management**
- [ ] test_interactive_card_addition()
- [ ] test_csv_import_validation()
- [ ] test_card_listing()
- [ ] test_unicode_support()
- [ ] test_csv_edge_cases()

**Review System**
- [ ] test_card_ordering()
- [ ] test_terminal_interface()
- [ ] test_rating_options()
- [ ] test_session_interruption()
- [ ] test_progress_indicator()

**SM-2 Algorithm**
- [ ] test_learning_steps()
- [ ] test_sm2_intervals()
- [ ] test_ease_factor_adjustments()
- [ ] test_interval_bounds()
- [ ] test_fuzz_factor()
- [ ] test_due_date_accuracy()

**CLI Interface**
- [ ] test_all_commands()
- [ ] test_help_text()
- [ ] test_error_messages()
- [ ] test_argument_parsing()
- [ ] test_exit_codes()

**Performance & Reliability**
- [ ] test_100_card_review_time()
- [ ] test_database_operation_time()
- [ ] test_no_data_loss()
- [ ] test_corruption_handling()
- [ ] test_memory_usage()

---

## Quality Assurance Checklist

### Code Quality
- [ ] All functions have docstrings
- [ ] Code follows PEP 8 style guidelines
- [ ] No code duplication
- [ ] Proper error handling throughout
- [ ] Comprehensive logging

### Testing
- [ ] 95%+ test coverage achieved
- [ ] All acceptance criteria tests pass
- [ ] Performance benchmarks met
- [ ] Edge cases covered
- [ ] Regression tests in place

### Documentation
- [ ] README updated with usage instructions
- [ ] API documentation complete
- [ ] Command help text comprehensive
- [ ] Installation instructions clear

### Performance
- [ ] Database operations <100ms
- [ ] Review sessions efficient
- [ ] Memory usage optimized
- [ ] Large dataset handling

### User Experience
- [ ] Clear error messages
- [ ] Intuitive command interface
- [ ] Helpful feedback
- [ ] Graceful error handling

---

## Risk Mitigation

### Technical Risks
- **Database corruption:** Implement backup/restore and validation
- **Algorithm bugs:** Extensive unit testing with known test cases
- **Performance issues:** Regular profiling and optimization
- **Memory leaks:** Proper resource management and testing

### Implementation Risks
- **Scope creep:** Stick to MVP requirements strictly
- **Time overrun:** Regular progress reviews and prioritization
- **Quality issues:** Maintain high test coverage throughout

### Mitigation Strategies
- Daily progress reviews
- Continuous integration testing
- Regular code reviews
- Performance monitoring
- User feedback incorporation

---

## Success Metrics

### Functional Metrics
- [ ] All 40+ acceptance criteria met
- [ ] Zero crashes in 1000 card review test
- [ ] Accurate SM-2 algorithm implementation
- [ ] Complete command interface

### Quality Metrics
- [ ] 95%+ test coverage
- [ ] <100ms database operations
- [ ] <5 minutes for 100 card reviews
- [ ] Proper error handling

### Deliverables
- [ ] Fully functional CLI application
- [ ] Comprehensive test suite
- [ ] Complete documentation
- [ ] Performance benchmarks

---

## Detailed Test Specifications

### SM-2 Algorithm Test Cases

**Test Case 1: New Card Learning Steps**
```python
def test_new_card_learning_steps():
    card = Card(front="test", back="test")

    # First review (rating 3)
    result = sm2_algorithm.review_card(card, rating=3)
    assert result.interval == 1  # 1 minute

    # Second review (rating 3)
    result = sm2_algorithm.review_card(card, rating=3)
    assert result.interval == 10  # 10 minutes

    # Third review (rating 3) - graduates to 1 day
    result = sm2_algorithm.review_card(card, rating=3)
    assert result.interval == 1440  # 1 day in minutes
```

**Test Case 2: SM-2 Interval Calculations**
```python
def test_sm2_interval_calculations():
    card = Card(interval=1, repetitions=1, ease_factor=2.5)

    # Rating 1 (Again) - Reset to learning
    result = sm2_algorithm.review_card(card, rating=1)
    assert result.interval == 1  # Back to 1 minute
    assert result.repetitions == 0

    # Rating 2 (Hard) - Decrease ease factor
    card = Card(interval=1, repetitions=1, ease_factor=2.5)
    result = sm2_algorithm.review_card(card, rating=2)
    assert result.ease_factor < 2.5

    # Rating 3 (Good) - Normal progression
    card = Card(interval=1, repetitions=1, ease_factor=2.5)
    result = sm2_algorithm.review_card(card, rating=3)
    assert result.interval == 6  # 1 * 2.5 = 2.5, rounded to 6 days

    # Rating 4 (Easy) - Easy bonus
    card = Card(interval=1, repetitions=1, ease_factor=2.5)
    result = sm2_algorithm.review_card(card, rating=4)
    assert result.interval == int(6 * 1.3)  # Easy bonus applied
```

### Database Test Cases

**Test Case 3: Foreign Key Constraints**
```python
def test_foreign_key_constraints():
    db = Database()

    # Create deck
    deck_id = db.create_deck("Test Deck")

    # Create card with valid deck_id
    card_id = db.create_card(deck_id, "front", "back")
    assert card_id is not None

    # Try to create card with invalid deck_id
    with pytest.raises(IntegrityError):
        db.create_card(999, "front", "back")

    # Delete deck should cascade to cards
    db.delete_deck(deck_id)
    cards = db.get_cards_by_deck(deck_id)
    assert len(cards) == 0
```

### CLI Test Cases

**Test Case 4: Command Line Interface**
```python
def test_cli_commands():
    runner = CliRunner()

    # Test create-deck command
    result = runner.invoke(cli, ['create-deck', 'Test Deck'])
    assert result.exit_code == 0
    assert 'created' in result.output.lower()

    # Test duplicate deck error
    result = runner.invoke(cli, ['create-deck', 'Test Deck'])
    assert result.exit_code != 0
    assert 'already exists' in result.output.lower()

    # Test list-decks command
    result = runner.invoke(cli, ['list-decks'])
    assert result.exit_code == 0
    assert 'Test Deck' in result.output
```

### Performance Test Cases

**Test Case 5: Performance Benchmarks**
```python
def test_performance_benchmarks():
    db = Database()

    # Create large dataset
    deck_id = db.create_deck("Performance Test")
    for i in range(10000):
        db.create_card(deck_id, f"front_{i}", f"back_{i}")

    # Test query performance
    start_time = time.time()
    due_cards = db.get_due_cards(deck_id)
    query_time = time.time() - start_time

    assert query_time < 0.1  # <100ms requirement

    # Test review session performance
    start_time = time.time()
    for i in range(100):
        card = due_cards[i % len(due_cards)]
        sm2_algorithm.review_card(card, rating=3)
    session_time = time.time() - start_time

    assert session_time < 300  # <5 minutes for 100 cards
```

This comprehensive implementation plan provides a complete roadmap for building the MVP spaced repetition system with detailed task breakdowns, time estimates, dependencies, and extensive test specifications to ensure 95%+ test coverage and meet all acceptance criteria.
