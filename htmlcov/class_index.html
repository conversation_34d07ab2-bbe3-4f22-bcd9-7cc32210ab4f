<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">78%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-30 07:39 -0700
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t18">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t18"><data value='Database'>Database</data></a></td>
                <td>106</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="93 106">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="43 43">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t19">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t19"><data value='Deck'>Deck</data></a></td>
                <td>53</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="13 53">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t192">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t192"><data value='Card'>Card</data></a></td>
                <td>66</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="40 66">61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t492">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t492"><data value='Review'>Review</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t561">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t561"><data value='Tag'>Tag</data></a></td>
                <td>59</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="59 59">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>81</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="81 81">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>421</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="329 421">78%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-30 07:39 -0700
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
