{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.1", "globals": "fc84ad9eb4b525683e194a8eb9729afe", "files": {"z_d1689845591154e7_database_py": {"hash": "ae050b9b89751e6192b7e4ac74a447e5", "index": {"url": "z_d1689845591154e7_database_py.html", "file": "srs/database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 149, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d1689845591154e7_models_py": {"hash": "897065ce8c978c27f1c26ed5cb981931", "index": {"url": "z_d1689845591154e7_models_py.html", "file": "srs/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 272, "n_excluded": 0, "n_missing": 79, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}