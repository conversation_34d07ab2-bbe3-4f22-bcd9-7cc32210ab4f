<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">78%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-30 07:39 -0700
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t26">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t26"><data value='init__'>Database.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t37">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t37"><data value='get_default_db_path'>Database._get_default_db_path</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t43">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t43"><data value='ensure_database_exists'>Database._ensure_database_exists</data></a></td>
                <td>16</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="8 16">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t68">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t68"><data value='connection'>Database.connection</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t83">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t83"><data value='transaction'>Database.transaction</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t93">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t93"><data value='create_schema'>Database._create_schema</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t119">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t119"><data value='get_schema_version'>Database._get_schema_version</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t128">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t128"><data value='apply_migration_v1'>Database._apply_migration_v1</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t174">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t174"><data value='apply_pending_migrations'>Database._apply_pending_migrations</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t182">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t182"><data value='apply_migration_v2'>Database._apply_migration_v2</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t218">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t218"><data value='create_tag'>Database.create_tag</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t223">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t223"><data value='get_tag_by_name'>Database.get_tag_by_name</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t229">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t229"><data value='get_tag_by_id'>Database.get_tag_by_id</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t235">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t235"><data value='get_all_tags'>Database.get_all_tags</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t240">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t240"><data value='update_tag'>Database.update_tag</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t259">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t259"><data value='delete_tag'>Database.delete_tag</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t264">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t264"><data value='add_card_tag'>Database.add_card_tag</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t276">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t276"><data value='remove_card_tag'>Database.remove_card_tag</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t281">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t281"><data value='get_card_tags'>Database.get_card_tags</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t291">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t291"><data value='get_cards_by_tag'>Database.get_cards_by_tag</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t301">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t301"><data value='get_tag_usage_stats'>Database.get_tag_usage_stats</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t312">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t312"><data value='execute_query'>Database.execute_query</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t327">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t327"><data value='execute_update'>Database.execute_update</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t342">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t342"><data value='close'>Database.close</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t353">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t353"><data value='get_database'>get_database</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t369">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html#t369"><data value='reset_database'>reset_database</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_database_py.html">srs/database.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_database_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t32">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t32"><data value='post_init__'>Deck.__post_init__</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t41">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t41"><data value='create'>Deck.create</data></a></td>
                <td>14</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="10 14">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t81">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t81"><data value='get_by_id'>Deck.get_by_id</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t99">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t99"><data value='get_by_name'>Deck.get_by_name</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t117">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t117"><data value='get_all'>Deck.get_all</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t133">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t133"><data value='delete'>Deck.delete</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t154">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t154"><data value='get_card_counts'>Deck.get_card_counts</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t217">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t217"><data value='post_init__'>Card.__post_init__</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t231">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t231"><data value='create'>Card.create</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t266">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t266"><data value='get_by_id'>Card.get_by_id</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t291">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t291"><data value='get_by_deck'>Card.get_by_deck</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t317">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t317"><data value='get_due_cards'>Card.get_due_cards</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t360">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t360"><data value='save'>Card.save</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t374">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t374"><data value='get_tags'>Card.get_tags</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t397">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t397"><data value='add_tag'>Card.add_tag</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t413">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t413"><data value='add_tag_by_name'>Card.add_tag_by_name</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t429">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t429"><data value='remove_tag'>Card.remove_tag</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t445">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t445"><data value='remove_tag_by_name'>Card.remove_tag_by_name</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t461">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t461"><data value='has_tag'>Card.has_tag</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t477">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t477"><data value='has_tag_by_name'>Card.has_tag_by_name</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t507">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t507"><data value='post_init__'>Review.__post_init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t516">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t516"><data value='create'>Review.create</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t540">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t540"><data value='get_by_card'>Review.get_by_card</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t576">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t576"><data value='post_init__'>Tag.__post_init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t589">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t589"><data value='create'>Tag.create</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t628">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t628"><data value='get_by_id'>Tag.get_by_id</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t652">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t652"><data value='get_by_name'>Tag.get_by_name</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t676">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t676"><data value='get_all'>Tag.get_all</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t697">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t697"><data value='get_usage_stats'>Tag.get_usage_stats</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t720">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t720"><data value='update'>Tag.update</data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t763">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t763"><data value='delete'>Tag.delete</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t776">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html#t776"><data value='get_cards'>Tag.get_cards</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d1689845591154e7_models_py.html">srs/models.py</a></td>
                <td class="name left"><a href="z_d1689845591154e7_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>81</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="81 81">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>421</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="329 421">78%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-30 07:39 -0700
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
