"""
Test tag functionality for Phase 1.1.1.

This module tests the Tag model, tag-related database operations,
and card-tag relationships to achieve 95%+ test coverage.
"""

import pytest
import tempfile
from pathlib import Path

from srs.database import reset_database
from srs.models import Tag, Card, Deck


class TestTagModel:
    """Test Tag model functionality."""
    
    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
    
    def test_tag_creation_basic(self):
        """Test basic tag creation."""
        tag = Tag.create('Spanish')
        
        assert tag.id is not None
        assert tag.name == 'Spanish'
        assert tag.color == '#808080'  # Default color
        assert tag.created_at is not None
    
    def test_tag_creation_with_color(self):
        """Test tag creation with custom color."""
        tag = Tag.create('French', '#FF0000')
        
        assert tag.name == 'French'
        assert tag.color == '#FF0000'
    
    def test_tag_creation_validation(self):
        """Test tag creation validation."""
        # Empty name should raise error
        with pytest.raises(ValueError, match="Tag name cannot be empty"):
            Tag.create('')
        
        # Whitespace-only name should raise error
        with pytest.raises(ValueError, match="Tag name cannot be empty"):
            Tag.create('   ')
    
    def test_tag_duplicate_name(self):
        """Test that duplicate tag names are not allowed."""
        Tag.create('Spanish')
        
        # Creating another tag with same name should raise error
        with pytest.raises(ValueError, match="Tag 'Spanish' already exists"):
            Tag.create('Spanish')
    
    def test_tag_name_normalization(self):
        """Test that tag names are normalized (trimmed)."""
        tag = Tag.create('  Spanish  ')
        assert tag.name == 'Spanish'
    
    def test_tag_get_by_id(self):
        """Test getting tag by ID."""
        created_tag = Tag.create('Spanish')
        
        retrieved_tag = Tag.get_by_id(created_tag.id)
        assert retrieved_tag is not None
        assert retrieved_tag.id == created_tag.id
        assert retrieved_tag.name == created_tag.name
        assert retrieved_tag.color == created_tag.color
    
    def test_tag_get_by_id_not_found(self):
        """Test getting non-existent tag by ID."""
        tag = Tag.get_by_id(999)
        assert tag is None
    
    def test_tag_get_by_name(self):
        """Test getting tag by name."""
        created_tag = Tag.create('Spanish')
        
        retrieved_tag = Tag.get_by_name('Spanish')
        assert retrieved_tag is not None
        assert retrieved_tag.id == created_tag.id
        assert retrieved_tag.name == created_tag.name
    
    def test_tag_get_by_name_not_found(self):
        """Test getting non-existent tag by name."""
        tag = Tag.get_by_name('NonExistent')
        assert tag is None
    
    def test_tag_get_all(self):
        """Test getting all tags."""
        # Create multiple tags
        Tag.create('Spanish')
        Tag.create('French')
        Tag.create('German')
        
        all_tags = Tag.get_all()
        assert len(all_tags) == 3
        
        # Should be sorted by name
        tag_names = [tag.name for tag in all_tags]
        assert tag_names == ['French', 'German', 'Spanish']
    
    def test_tag_update(self):
        """Test updating tag properties."""
        tag = Tag.create('Spanish')
        
        # Update name
        success = tag.update(name='Español')
        assert success is True
        assert tag.name == 'Español'
        
        # Update color
        success = tag.update(color='#FF0000')
        assert success is True
        assert tag.color == '#FF0000'
        
        # Update both
        success = tag.update(name='Spanish', color='#00FF00')
        assert success is True
        assert tag.name == 'Spanish'
        assert tag.color == '#00FF00'
    
    def test_tag_update_validation(self):
        """Test tag update validation."""
        tag1 = Tag.create('Spanish')
        Tag.create('French')
        
        # Cannot update to empty name
        with pytest.raises(ValueError, match="Tag name cannot be empty"):
            tag1.update(name='')
        
        # Cannot update to existing name
        with pytest.raises(ValueError, match="Tag 'French' already exists"):
            tag1.update(name='French')
    
    def test_tag_update_without_id(self):
        """Test that updating tag without ID raises error."""
        tag = Tag('Spanish')  # No ID
        
        with pytest.raises(ValueError, match="Cannot update tag without ID"):
            tag.update(name='Español')
    
    def test_tag_delete(self):
        """Test tag deletion."""
        tag = Tag.create('Spanish')
        tag_id = tag.id
        
        success = tag.delete()
        assert success is True
        
        # Tag should no longer exist
        retrieved_tag = Tag.get_by_id(tag_id)
        assert retrieved_tag is None
    
    def test_tag_delete_without_id(self):
        """Test that deleting tag without ID returns False."""
        tag = Tag('Spanish')  # No ID
        success = tag.delete()
        assert success is False


class TestCardTagRelationships:
    """Test card-tag relationship functionality."""
    
    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
    
    def test_add_tag_to_card(self):
        """Test adding tag to card."""
        # Create deck, card, and tag
        deck = Deck.create('Spanish')
        card = Card.create(deck.id, 'hola', 'hello')
        tag = Tag.create('greetings')
        
        # Add tag to card
        success = card.add_tag(tag)
        assert success is True
        
        # Verify tag was added
        card_tags = card.get_tags()
        assert len(card_tags) == 1
        assert card_tags[0].id == tag.id
        assert card_tags[0].name == 'greetings'
    
    def test_add_tag_by_name_to_card(self):
        """Test adding tag to card by name."""
        # Create deck, card, and tag
        deck = Deck.create('Spanish')
        card = Card.create(deck.id, 'hola', 'hello')
        tag = Tag.create('greetings')
        
        # Add tag by name
        success = card.add_tag_by_name('greetings')
        assert success is True
        
        # Verify tag was added
        assert card.has_tag(tag) is True
    
    def test_remove_tag_from_card(self):
        """Test removing tag from card."""
        # Create deck, card, and tag
        deck = Deck.create('Spanish')
        card = Card.create(deck.id, 'hola', 'hello')
        tag = Tag.create('greetings')
        
        # Add and then remove tag
        card.add_tag(tag)
        success = card.remove_tag(tag)
        assert success is True
        
        # Verify tag was removed
        card_tags = card.get_tags()
        assert len(card_tags) == 0
    
    def test_has_tag_functionality(self):
        """Test checking if card has specific tag."""
        # Create deck, card, and tags
        deck = Deck.create('Spanish')
        card = Card.create(deck.id, 'hola', 'hello')
        tag1 = Tag.create('greetings')
        tag2 = Tag.create('verbs')
        
        # Initially no tags
        assert card.has_tag(tag1) is False
        assert card.has_tag_by_name('greetings') is False
        
        # Add one tag
        card.add_tag(tag1)
        assert card.has_tag(tag1) is True
        assert card.has_tag(tag2) is False
        assert card.has_tag_by_name('greetings') is True
        assert card.has_tag_by_name('verbs') is False
    
    def test_multiple_tags_per_card(self):
        """Test that cards can have multiple tags."""
        # Create deck, card, and multiple tags
        deck = Deck.create('Spanish')
        card = Card.create(deck.id, 'hola', 'hello')
        tag1 = Tag.create('greetings')
        tag2 = Tag.create('common')
        tag3 = Tag.create('beginner')
        
        # Add multiple tags to card
        card.add_tag(tag1)
        card.add_tag(tag2)
        card.add_tag(tag3)
        
        # Verify all tags are associated
        card_tags = card.get_tags()
        assert len(card_tags) == 3
        
        tag_names = [tag.name for tag in card_tags]
        assert 'greetings' in tag_names
        assert 'common' in tag_names
        assert 'beginner' in tag_names
    
    def test_tag_deletion_removes_associations(self):
        """Test that deleting a tag removes all card associations."""
        # Create deck, card, and tag
        deck = Deck.create('Spanish')
        card = Card.create(deck.id, 'hola', 'hello')
        tag = Tag.create('greetings')
        
        # Add tag to card
        card.add_tag(tag)
        assert len(card.get_tags()) == 1
        
        # Delete tag
        tag.delete()
        
        # Card should no longer have any tags
        card_tags = card.get_tags()
        assert len(card_tags) == 0

    def test_get_cards_by_tag(self):
        """Test getting cards associated with a tag."""
        # Create deck, cards, and tag
        deck = Deck.create('Spanish')
        card1 = Card.create(deck.id, 'hola', 'hello')
        card2 = Card.create(deck.id, 'adiós', 'goodbye')
        card3 = Card.create(deck.id, 'comer', 'to eat')
        tag = Tag.create('greetings')

        # Add tag to some cards
        card1.add_tag(tag)
        card2.add_tag(tag)

        # Get cards by tag
        tagged_cards = tag.get_cards()
        assert len(tagged_cards) == 2

        # Should include card1 and card2, but not card3
        card_ids = [card.id for card in tagged_cards]
        assert card1.id in card_ids
        assert card2.id in card_ids
        assert card3.id not in card_ids

    def test_tag_usage_stats(self):
        """Test tag usage statistics."""
        # Create deck and cards
        deck = Deck.create('Spanish')
        card1 = Card.create(deck.id, 'hola', 'hello')
        card2 = Card.create(deck.id, 'adiós', 'goodbye')
        card3 = Card.create(deck.id, 'comer', 'to eat')

        # Create tags
        tag1 = Tag.create('greetings')  # Will have 2 cards
        tag2 = Tag.create('verbs')      # Will have 1 card
        tag3 = Tag.create('unused')     # Will have 0 cards

        # Add tags to cards
        card1.add_tag(tag1)
        card2.add_tag(tag1)
        card3.add_tag(tag2)

        # Get usage stats
        stats = Tag.get_usage_stats()
        assert len(stats) == 3

        # Should be sorted by usage count (descending)
        assert stats[0]['card_count'] == 2  # greetings
        assert stats[0]['tag'].name == 'greetings'
        assert stats[1]['card_count'] == 1  # verbs
        assert stats[1]['tag'].name == 'verbs'
        assert stats[2]['card_count'] == 0  # unused
        assert stats[2]['tag'].name == 'unused'


class TestTagDatabaseOperations:
    """Test tag-related database operations."""

    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)

    def teardown_method(self):
        """Clean up after each test."""
        reset_database()

    def test_create_tag_database(self):
        """Test creating tag in database."""
        tag_id = self.db.create_tag('Spanish', '#FF0000')
        assert tag_id > 0

        # Verify tag was created
        tag_row = self.db.get_tag_by_id(tag_id)
        assert tag_row is not None
        assert tag_row['name'] == 'Spanish'
        assert tag_row['color'] == '#FF0000'

    def test_get_tag_by_name_database(self):
        """Test getting tag by name from database."""
        tag_id = self.db.create_tag('Spanish')

        tag_row = self.db.get_tag_by_name('Spanish')
        assert tag_row is not None
        assert tag_row['id'] == tag_id
        assert tag_row['name'] == 'Spanish'

    def test_get_all_tags_database(self):
        """Test getting all tags from database."""
        self.db.create_tag('Spanish')
        self.db.create_tag('French')

        tags = self.db.get_all_tags()
        assert len(tags) == 2

        # Should be sorted by name
        assert tags[0]['name'] == 'French'
        assert tags[1]['name'] == 'Spanish'

    def test_update_tag_database(self):
        """Test updating tag in database."""
        tag_id = self.db.create_tag('Spanish')

        # Update name only
        success = self.db.update_tag(tag_id, name='Español')
        assert success is True

        tag_row = self.db.get_tag_by_id(tag_id)
        assert tag_row['name'] == 'Español'

        # Update color only
        success = self.db.update_tag(tag_id, color='#FF0000')
        assert success is True

        tag_row = self.db.get_tag_by_id(tag_id)
        assert tag_row['color'] == '#FF0000'

    def test_delete_tag_database(self):
        """Test deleting tag from database."""
        tag_id = self.db.create_tag('Spanish')

        success = self.db.delete_tag(tag_id)
        assert success is True

        # Tag should no longer exist
        tag_row = self.db.get_tag_by_id(tag_id)
        assert tag_row is None

    def test_card_tag_database_operations(self):
        """Test low-level card-tag database operations."""
        # Create deck, card, and tag
        deck_id = self.db.execute_update("INSERT INTO decks (name) VALUES (?)", ('Spanish',))
        card_id = self.db.execute_update(
            "INSERT INTO cards (deck_id, front, back) VALUES (?, ?, ?)",
            (deck_id, 'hola', 'hello')
        )
        tag_id = self.db.create_tag('greetings')

        # Test add_card_tag
        success = self.db.add_card_tag(card_id, tag_id)
        assert success is True

        # Test duplicate add (should return False)
        success = self.db.add_card_tag(card_id, tag_id)
        assert success is False  # Already exists

        # Test get_card_tags
        tags = self.db.get_card_tags(card_id)
        assert len(tags) == 1
        assert tags[0]['id'] == tag_id

        # Test get_cards_by_tag
        cards = self.db.get_cards_by_tag(tag_id)
        assert len(cards) == 1
        assert cards[0]['id'] == card_id

        # Test remove_card_tag
        success = self.db.remove_card_tag(card_id, tag_id)
        assert success is True

        # Verify removal
        tags = self.db.get_card_tags(card_id)
        assert len(tags) == 0

    def test_tag_usage_stats_database(self):
        """Test tag usage statistics from database."""
        # Create deck and cards
        deck_id = self.db.execute_update("INSERT INTO decks (name) VALUES (?)", ('Spanish',))
        card1_id = self.db.execute_update(
            "INSERT INTO cards (deck_id, front, back) VALUES (?, ?, ?)",
            (deck_id, 'hola', 'hello')
        )
        card2_id = self.db.execute_update(
            "INSERT INTO cards (deck_id, front, back) VALUES (?, ?, ?)",
            (deck_id, 'adiós', 'goodbye')
        )

        # Create tags
        tag1_id = self.db.create_tag('greetings')
        tag2_id = self.db.create_tag('unused')

        # Add tags to cards
        self.db.add_card_tag(card1_id, tag1_id)
        self.db.add_card_tag(card2_id, tag1_id)

        # Get usage stats
        stats = self.db.get_tag_usage_stats()
        assert len(stats) == 2

        # Should be sorted by usage count (descending)
        assert stats[0]['card_count'] == 2  # greetings
        assert stats[0]['name'] == 'greetings'
        assert stats[1]['card_count'] == 0  # unused
        assert stats[1]['name'] == 'unused'


class TestTagEdgeCases:
    """Test edge cases and error conditions for tags."""

    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)

    def teardown_method(self):
        """Clean up after each test."""
        reset_database()

    def test_tag_with_invalid_color(self):
        """Test tag creation with invalid color defaults to gray."""
        tag = Tag('Spanish', 'invalid-color')
        assert tag.color == '#808080'  # Should default to gray

    def test_tag_post_init_validation(self):
        """Test tag validation in __post_init__."""
        # Empty name should raise error
        with pytest.raises(ValueError, match="Tag name cannot be empty"):
            Tag('')

        # Whitespace name should raise error
        with pytest.raises(ValueError, match="Tag name cannot be empty"):
            Tag('   ')

    def test_tag_operations_without_id(self):
        """Test tag operations when tag has no ID."""
        tag = Tag('Spanish')  # No ID

        # get_cards should return empty list
        cards = tag.get_cards()
        assert cards == []

    def test_card_tag_operations_without_ids(self):
        """Test card-tag operations when objects have no IDs."""
        card = Card(deck_id=1, front='front', back='back')  # No ID
        tag = Tag('Spanish')  # No ID

        # Operations should return False
        assert card.add_tag(tag) is False
        assert card.remove_tag(tag) is False
        assert card.has_tag(tag) is False

    def test_add_nonexistent_tag_by_name(self):
        """Test adding non-existent tag by name."""
        deck = Deck.create('Spanish')
        card = Card.create(deck.id, 'hola', 'hello')

        # Should return False for non-existent tag
        success = card.add_tag_by_name('nonexistent')
        assert success is False

    def test_remove_nonexistent_tag_by_name(self):
        """Test removing non-existent tag by name."""
        deck = Deck.create('Spanish')
        card = Card.create(deck.id, 'hola', 'hello')

        # Should return False for non-existent tag
        success = card.remove_tag_by_name('nonexistent')
        assert success is False

    def test_tag_update_no_changes(self):
        """Test tag update with no actual changes."""
        tag = Tag.create('Spanish')

        # Update with no parameters should return False
        success = tag.update()
        assert success is False

    def test_database_constraint_violations(self):
        """Test database constraint handling."""
        # Test duplicate tag creation at database level
        self.db.create_tag('Spanish')

        # Attempting to create duplicate should raise error
        with pytest.raises(Exception):  # SQLite integrity error
            self.db.create_tag('Spanish')


class TestTagMigration:
    """Test database migration for tags system."""

    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)

    def teardown_method(self):
        """Clean up after each test."""
        reset_database()

    def test_schema_version_tracking(self):
        """Test that schema version is tracked correctly."""
        # Check current schema version
        version = self.db._get_schema_version()
        assert version >= 2  # Should be at least version 2 with tags (may be higher with difficulty tracking)

    def test_tags_table_exists(self):
        """Test that tags table was created."""
        result = self.db.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='tags'"
        )
        assert len(result) == 1

    def test_card_tags_table_exists(self):
        """Test that card_tags table was created."""
        result = self.db.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='card_tags'"
        )
        assert len(result) == 1

    def test_tags_table_structure(self):
        """Test that tags table has correct structure."""
        result = self.db.execute_query("PRAGMA table_info(tags)")
        columns = {row['name']: row['type'] for row in result}

        assert 'id' in columns
        assert 'name' in columns
        assert 'color' in columns
        assert 'created_at' in columns

    def test_card_tags_table_structure(self):
        """Test that card_tags table has correct structure."""
        result = self.db.execute_query("PRAGMA table_info(card_tags)")
        columns = {row['name']: row['type'] for row in result}

        assert 'card_id' in columns
        assert 'tag_id' in columns
        assert 'created_at' in columns

    def test_foreign_key_constraints(self):
        """Test that foreign key constraints are properly set."""
        # Test card_tags foreign keys
        result = self.db.execute_query("PRAGMA foreign_key_list(card_tags)")
        fk_tables = [row['table'] for row in result]

        assert 'cards' in fk_tables
        assert 'tags' in fk_tables

    def test_indexes_created(self):
        """Test that performance indexes were created."""
        result = self.db.execute_query(
            "SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'"
        )
        index_names = [row['name'] for row in result]

        assert 'idx_card_tags_card_id' in index_names
        assert 'idx_card_tags_tag_id' in index_names
        assert 'idx_tags_name' in index_names
