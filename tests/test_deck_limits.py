"""
Test deck limits and settings functionality for Phase 1.1.3.

This module tests the deck limits system, including database migration,
deck settings management, daily limit enforcement, and review session integration.
"""

import pytest
import tempfile
import json
from pathlib import Path
from datetime import datetime, timedelta

from srs.database import reset_database
from srs.models import Card, Deck, Review
from srs.review import create_review_session


class TestDeckLimitsAndSettings:
    """Test deck limits and settings functionality."""
    
    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
    
    def test_deck_creation_with_default_limits(self):
        """Test that new decks have correct default limits."""
        deck = Deck.create('Test Deck')
        
        assert deck.daily_new_limit == 20
        assert deck.daily_review_limit == 200
        assert deck.description == ""
        assert deck.settings == "{}"
    
    def test_deck_creation_with_custom_limits(self):
        """Test creating deck with custom limits."""
        deck = Deck(
            name='Custom Deck',
            daily_new_limit=10,
            daily_review_limit=50,
            description='Test description',
            settings='{"theme": "dark"}'
        )
        
        # Create in database
        from srs.database import get_database
        db = get_database()
        deck_id = db.execute_update(
            """INSERT INTO decks (name, daily_new_limit, daily_review_limit, 
               description, settings) VALUES (?, ?, ?, ?, ?)""",
            (deck.name, deck.daily_new_limit, deck.daily_review_limit,
             deck.description, deck.settings)
        )
        deck.id = deck_id
        
        # Reload and verify
        reloaded_deck = Deck.get_by_name('Custom Deck')
        assert reloaded_deck.daily_new_limit == 10
        assert reloaded_deck.daily_review_limit == 50
        assert reloaded_deck.description == 'Test description'
        assert reloaded_deck.settings == '{"theme": "dark"}'
    
    def test_deck_save_method(self):
        """Test deck save method updates all fields."""
        deck = Deck.create('Test Deck')
        
        # Update fields
        deck.daily_new_limit = 15
        deck.daily_review_limit = 100
        deck.description = 'Updated description'
        deck.settings = '{"updated": true}'
        
        # Save changes
        success = deck.save()
        assert success is True
        
        # Reload and verify
        reloaded_deck = Deck.get_by_name('Test Deck')
        assert reloaded_deck.daily_new_limit == 15
        assert reloaded_deck.daily_review_limit == 100
        assert reloaded_deck.description == 'Updated description'
        assert reloaded_deck.settings == '{"updated": true}'
    
    def test_update_settings_method(self):
        """Test deck update_settings method."""
        deck = Deck.create('Test Deck')
        
        # Update settings
        success = deck.update_settings(
            daily_new_limit=25,
            daily_review_limit=150,
            description='New description'
        )
        assert success is True
        
        # Verify changes
        assert deck.daily_new_limit == 25
        assert deck.daily_review_limit == 150
        assert deck.description == 'New description'
        
        # Verify in database
        reloaded_deck = Deck.get_by_name('Test Deck')
        assert reloaded_deck.daily_new_limit == 25
        assert reloaded_deck.daily_review_limit == 150
        assert reloaded_deck.description == 'New description'
    
    def test_get_daily_limits(self):
        """Test get_daily_limits method."""
        deck = Deck.create('Test Deck')
        deck.daily_new_limit = 30
        deck.daily_review_limit = 250
        
        limits = deck.get_daily_limits()
        assert limits['new'] == 30
        assert limits['review'] == 250
    
    def test_get_settings_dict(self):
        """Test get_settings_dict method."""
        deck = Deck.create('Test Deck')
        
        # Test empty settings
        settings = deck.get_settings_dict()
        assert settings == {}
        
        # Test valid JSON settings
        deck.settings = '{"theme": "dark", "auto_advance": true}'
        settings = deck.get_settings_dict()
        assert settings == {"theme": "dark", "auto_advance": True}
        
        # Test invalid JSON (should return empty dict)
        deck.settings = 'invalid json'
        settings = deck.get_settings_dict()
        assert settings == {}
    
    def test_check_daily_limits_no_reviews(self):
        """Test daily limits check with no reviews done today."""
        deck = Deck.create('Test Deck')
        
        limits = deck.check_daily_limits()
        
        assert limits['new_studied_today'] == 0
        assert limits['reviews_done_today'] == 0
        assert limits['new_remaining'] == 20
        assert limits['review_remaining'] == 200
        assert limits['can_study_new'] is True
        assert limits['can_study_reviews'] is True
        assert limits['can_study'] is True
    
    def test_check_daily_limits_with_reviews(self):
        """Test daily limits check with some reviews done today."""
        deck = Deck.create('Test Deck')
        
        # Create cards and reviews
        card1 = Card.create(deck.id, 'front1', 'back1')
        card2 = Card.create(deck.id, 'front2', 'back2')
        
        # Simulate reviews done today
        card1.repetitions = 1  # New card studied
        card1.save()
        card2.repetitions = 2  # Review card
        card2.save()
        
        # Create review records for today
        Review.create(card1.id, 3)
        Review.create(card2.id, 3)
        
        limits = deck.check_daily_limits()
        
        assert limits['new_studied_today'] == 1
        assert limits['reviews_done_today'] == 2
        assert limits['new_remaining'] == 19
        assert limits['review_remaining'] == 198
        assert limits['can_study_new'] is True
        assert limits['can_study_reviews'] is True
        assert limits['can_study'] is True
    
    def test_check_daily_limits_exhausted(self):
        """Test daily limits when limits are exhausted."""
        deck = Deck.create('Test Deck')
        deck.daily_new_limit = 1
        deck.daily_review_limit = 1
        deck.save()
        
        # Create cards and exhaust limits
        card1 = Card.create(deck.id, 'front1', 'back1')
        card2 = Card.create(deck.id, 'front2', 'back2')
        
        # Simulate limits being reached
        card1.repetitions = 1  # New card studied
        card1.save()
        card2.repetitions = 2  # Review card
        card2.save()
        
        # Create review records for today
        Review.create(card1.id, 3)
        Review.create(card2.id, 3)
        
        limits = deck.check_daily_limits()
        
        assert limits['new_studied_today'] == 1
        assert limits['reviews_done_today'] == 2
        assert limits['new_remaining'] == 0
        assert limits['review_remaining'] == 0
        assert limits['can_study_new'] is False
        assert limits['can_study_reviews'] is False
        assert limits['can_study'] is False
    
    def test_get_limited_due_cards_no_limits(self):
        """Test getting limited due cards when no limits are reached."""
        deck = Deck.create('Test Deck')
        
        # Create mix of new and due cards
        new_card = Card.create(deck.id, 'new', 'new')
        due_card = Card.create(deck.id, 'due', 'due')
        due_card.repetitions = 1
        due_card.due_date = datetime.now() - timedelta(hours=1)  # Past due
        due_card.save()
        
        limited_cards = deck.get_limited_due_cards(include_new=True)
        
        # Should include both cards
        assert len(limited_cards) == 2
        card_fronts = [card.front for card in limited_cards]
        assert 'new' in card_fronts
        assert 'due' in card_fronts
    
    def test_get_limited_due_cards_with_limits(self):
        """Test getting limited due cards when limits are applied."""
        deck = Deck.create('Test Deck')
        deck.daily_new_limit = 1
        deck.daily_review_limit = 1
        deck.save()
        
        # Create multiple new and due cards
        for i in range(3):
            new_card = Card.create(deck.id, f'new{i}', f'new{i}')
            due_card = Card.create(deck.id, f'due{i}', f'due{i}')
            due_card.repetitions = 1
            due_card.due_date = datetime.now() - timedelta(hours=1)
            due_card.save()
        
        limited_cards = deck.get_limited_due_cards(include_new=True)
        
        # Should be limited to 1 new + 1 review = 2 cards total
        assert len(limited_cards) <= 2
    
    def test_get_limited_due_cards_exclude_new(self):
        """Test getting limited due cards excluding new cards."""
        deck = Deck.create('Test Deck')
        
        # Create mix of new and due cards
        new_card = Card.create(deck.id, 'new', 'new')
        due_card = Card.create(deck.id, 'due', 'due')
        due_card.repetitions = 1
        due_card.due_date = datetime.now() - timedelta(hours=1)
        due_card.save()
        
        limited_cards = deck.get_limited_due_cards(include_new=False)
        
        # Should only include due card
        assert len(limited_cards) == 1
        assert limited_cards[0].front == 'due'


class TestDeckLimitsDatabaseMigration:
    """Test database migration for deck limits."""
    
    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
    
    def test_schema_version_4(self):
        """Test that schema is at version 4 with deck limits."""
        version = self.db._get_schema_version()
        assert version == 4
    
    def test_deck_limit_columns_exist(self):
        """Test that deck limit columns exist."""
        result = self.db.execute_query("PRAGMA table_info(decks)")
        columns = {row['name']: row['type'] for row in result}
        
        assert 'daily_new_limit' in columns
        assert 'daily_review_limit' in columns
        assert 'description' in columns
        assert 'settings' in columns
    
    def test_migration_record_exists(self):
        """Test that migration v4 was recorded."""
        result = self.db.execute_query(
            "SELECT * FROM schema_version WHERE version = 4"
        )
        assert len(result) == 1
        assert result[0]['description'] == 'Phase 1.1.3: Deck limits and settings'


class TestReviewSessionLimitIntegration:
    """Test review session integration with deck limits."""
    
    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
    
    def test_review_session_respects_limits(self):
        """Test that review sessions respect deck limits."""
        deck = Deck.create('Test Deck')
        deck.daily_new_limit = 1
        deck.daily_review_limit = 1
        deck.save()
        
        # Create multiple cards
        for i in range(3):
            Card.create(deck.id, f'front{i}', f'back{i}')
        
        # Create review session
        session = create_review_session('Test Deck')
        
        # Should be limited by deck limits
        assert session is not None
        assert len(session.cards) <= 2  # Max 1 new + 1 review
    
    def test_review_session_no_cards_when_limits_exhausted(self):
        """Test review session when daily limits are exhausted."""
        deck = Deck.create('Test Deck')
        deck.daily_new_limit = 0
        deck.daily_review_limit = 0
        deck.save()

        # Create cards
        Card.create(deck.id, 'front', 'back')

        # Create review session
        session = create_review_session('Test Deck')

        # Should have no cards due to limits
        assert session is not None
        assert len(session.cards) == 0


class TestDeckLimitsEdgeCases:
    """Test edge cases and error conditions for deck limits."""

    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)

    def teardown_method(self):
        """Clean up after each test."""
        reset_database()

    def test_negative_limits_converted_to_zero(self):
        """Test that negative limits are converted to zero."""
        deck = Deck.create('Test Deck')

        success = deck.update_settings(
            daily_new_limit=-5,
            daily_review_limit=-10
        )
        assert success is True
        assert deck.daily_new_limit == 0
        assert deck.daily_review_limit == 0

    def test_save_deck_without_id_raises_error(self):
        """Test that saving deck without ID raises error."""
        deck = Deck(name='Test Deck')

        with pytest.raises(ValueError, match="Cannot save deck without ID"):
            deck.save()

    def test_check_daily_limits_deck_without_id(self):
        """Test daily limits check for deck without ID."""
        deck = Deck(name='Test Deck')

        limits = deck.check_daily_limits()
        assert limits['new_remaining'] == 0
        assert limits['review_remaining'] == 0
        assert limits['can_study'] is False

    def test_get_limited_due_cards_deck_without_id(self):
        """Test getting limited due cards for deck without ID."""
        deck = Deck(name='Test Deck')

        cards = deck.get_limited_due_cards()
        assert cards == []

    def test_update_settings_with_dict_settings(self):
        """Test updating settings with dictionary."""
        deck = Deck.create('Test Deck')

        settings_dict = {"theme": "dark", "auto_advance": True}
        success = deck.update_settings(settings=settings_dict)
        assert success is True

        # Verify settings were converted to JSON
        assert deck.settings == json.dumps(settings_dict)

        # Verify get_settings_dict returns the original dict
        retrieved_settings = deck.get_settings_dict()
        assert retrieved_settings == settings_dict

    def test_update_settings_with_string_settings(self):
        """Test updating settings with string."""
        deck = Deck.create('Test Deck')

        settings_string = '{"custom": "value"}'
        success = deck.update_settings(settings=settings_string)
        assert success is True

        assert deck.settings == settings_string

    def test_deck_loading_with_null_values(self):
        """Test deck loading when database has NULL values."""
        # Create deck directly in database with NULL values
        deck_id = self.db.execute_update(
            """INSERT INTO decks (name, daily_new_limit, daily_review_limit,
               description, settings) VALUES (?, NULL, NULL, NULL, NULL)""",
            ('Test Deck',)
        )

        # Loading should handle NULL values with defaults
        deck = Deck.get_by_name('Test Deck')
        assert deck.daily_new_limit == 20  # Default value
        assert deck.daily_review_limit == 200  # Default value
        assert deck.description == ""  # Default value
        assert deck.settings == "{}"  # Default value

    def test_get_all_decks_includes_new_fields(self):
        """Test that get_all includes new deck fields."""
        deck = Deck.create('Test Deck')
        deck.daily_new_limit = 15
        deck.daily_review_limit = 150
        deck.description = 'Test description'
        deck.save()

        all_decks = Deck.get_all()
        assert len(all_decks) == 1

        loaded_deck = all_decks[0]
        assert loaded_deck.daily_new_limit == 15
        assert loaded_deck.daily_review_limit == 150
        assert loaded_deck.description == 'Test description'

    def test_very_large_limits(self):
        """Test behavior with very large limit values."""
        deck = Deck.create('Test Deck')

        large_limit = 999999
        success = deck.update_settings(
            daily_new_limit=large_limit,
            daily_review_limit=large_limit
        )
        assert success is True
        assert deck.daily_new_limit == large_limit
        assert deck.daily_review_limit == large_limit

    def test_limits_with_timezone_considerations(self):
        """Test that daily limits work correctly across different times."""
        deck = Deck.create('Test Deck')

        # Create a card and review it
        card = Card.create(deck.id, 'front', 'back')
        card.repetitions = 1
        card.save()
        Review.create(card.id, 3)

        # Check limits - should show 1 review done today
        limits = deck.check_daily_limits()
        assert limits['reviews_done_today'] == 1
        assert limits['review_remaining'] == 199

    def test_concurrent_limit_checking(self):
        """Test limit checking with concurrent operations."""
        deck = Deck.create('Test Deck')

        # Create multiple cards
        cards = []
        for i in range(5):
            card = Card.create(deck.id, f'front{i}', f'back{i}')
            cards.append(card)

        # Check limits multiple times (simulating concurrent access)
        for _ in range(3):
            limits = deck.check_daily_limits()
            assert limits['new_remaining'] == 20
            assert limits['review_remaining'] == 200

    def test_limit_enforcement_with_mixed_card_types(self):
        """Test limit enforcement with mix of new and review cards."""
        deck = Deck.create('Test Deck')
        deck.daily_new_limit = 2
        deck.daily_review_limit = 2
        deck.save()

        # Create new cards
        for i in range(3):
            Card.create(deck.id, f'new{i}', f'new{i}')

        # Create due review cards
        for i in range(3):
            card = Card.create(deck.id, f'review{i}', f'review{i}')
            card.repetitions = 1
            card.due_date = datetime.now() - timedelta(hours=1)
            card.save()

        # Get limited cards
        limited_cards = deck.get_limited_due_cards(include_new=True)

        # Should respect both limits
        new_cards = [c for c in limited_cards if c.repetitions == 0]
        review_cards = [c for c in limited_cards if c.repetitions > 0]

        assert len(new_cards) <= 2
        assert len(review_cards) <= 2
        assert len(limited_cards) <= 4
