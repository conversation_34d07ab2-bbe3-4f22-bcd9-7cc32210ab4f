"""
Test project structure and basic imports.

This module tests that all modules can be imported correctly
and the basic project structure is working.
"""

import pytest
import sys
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TestProjectStructure:
    """Test basic project structure and imports."""
    
    def test_srs_package_import(self):
        """Test that the main SRS package can be imported."""
        import srs
        assert hasattr(srs, '__version__')
        assert hasattr(srs, '__author__')
    
    def test_database_module_import(self):
        """Test that the database module can be imported."""
        from srs import database
        assert hasattr(database, 'Database')
        assert hasattr(database, 'get_database')
    
    def test_models_module_import(self):
        """Test that the models module can be imported."""
        from srs import models
        assert hasattr(models, 'Deck')
        assert hasattr(models, 'Card')
        assert hasattr(models, 'Review')
    
    def test_algorithm_module_import(self):
        """Test that the algorithm module can be imported."""
        from srs import algorithm
        assert hasattr(algorithm, 'SM2Algorithm')
        assert hasattr(algorithm, 'SM2Config')
        assert hasattr(algorithm, 'get_algorithm')
    
    def test_review_module_import(self):
        """Test that the review module can be imported."""
        from srs import review
        assert hasattr(review, 'ReviewSession')
        assert hasattr(review, 'create_review_session')
    
    def test_utils_module_import(self):
        """Test that the utils module can be imported."""
        from srs import utils
        assert hasattr(utils, 'validate_deck_name')
        assert hasattr(utils, 'validate_card_content')
        assert hasattr(utils, 'parse_csv_file')
    
    def test_config_module_import(self):
        """Test that the config module can be imported."""
        from srs import config
        assert hasattr(config, 'SRSConfig')
        assert hasattr(config, 'ConfigManager')
        assert hasattr(config, 'get_config')
    
    def test_cli_module_import(self):
        """Test that the CLI module can be imported."""
        from srs import cli
        assert hasattr(cli, 'cli')
    
    def test_main_imports(self):
        """Test that main components can be imported from the package root."""
        from srs import (
            Deck, Card, Review,
            ReviewSession, create_review_session,
            SM2Config, get_algorithm,
            get_database, get_config,
            validate_deck_name, validate_card_content
        )
        
        # Just check that they're not None
        assert Deck is not None
        assert Card is not None
        assert Review is not None
        assert ReviewSession is not None
        assert create_review_session is not None
        assert SM2Config is not None
        assert get_algorithm is not None
        assert get_database is not None
        assert get_config is not None
        assert validate_deck_name is not None
        assert validate_card_content is not None


class TestLogging:
    """Test logging configuration."""
    
    def test_logging_setup(self):
        """Test that logging can be set up."""
        import logging
        from srs.config import SRSConfig, setup_logging
        
        config = SRSConfig(log_level='DEBUG')
        setup_logging(config)
        
        logger = logging.getLogger('test')
        logger.info("Test log message")
        
        # Check that logger is configured
        assert logger.level <= logging.INFO


class TestConfiguration:
    """Test configuration management."""
    
    def test_default_config(self):
        """Test default configuration creation."""
        from srs.config import SRSConfig
        
        config = SRSConfig()
        assert config.database_path.endswith('data.db')
        assert config.config_path.endswith('.srsrc')
        assert config.log_level == 'INFO'
    
    def test_config_manager(self):
        """Test configuration manager."""
        from srs.config import ConfigManager
        
        manager = ConfigManager()
        config = manager.load_config()
        
        assert config is not None
        assert hasattr(config, 'database_path')
        assert hasattr(config, 'log_level')


if __name__ == '__main__':
    pytest.main([__file__])
