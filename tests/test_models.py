"""
Test data models functionality.

This module tests the Deck, Card, and Review models including
CRUD operations, validation, relationships, and data persistence.
"""

import pytest
import tempfile
from datetime import datetime, timedelta
from pathlib import Path

from srs.database import reset_database
from srs.models import Deck, Card, Review


class TestDeck:
    """Test Deck model functionality."""
    
    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
    
    def test_deck_creation_with_valid_name(self):
        """Test creating a deck with a valid name."""
        deck = Deck.create("Spanish Verbs")
        
        assert deck.id is not None
        assert deck.name == "Spanish Verbs"
        assert deck.created_at is not None
        assert isinstance(deck.created_at, datetime)
    
    def test_deck_creation_with_invalid_names(self):
        """Test deck creation validation with invalid names."""
        # Empty name
        with pytest.raises(ValueError, match="Deck name cannot be empty"):
            Deck.create("")
        
        # Whitespace only
        with pytest.raises(ValueError, match="Deck name cannot be empty"):
            Deck.create("   ")
        
        # Too long name
        long_name = "x" * 256
        with pytest.raises(ValueError, match="Deck name too long"):
            Deck.create(long_name)
    
    def test_duplicate_deck_handling(self):
        """Test that duplicate deck names are not allowed."""
        Deck.create("Test Deck")
        
        with pytest.raises(ValueError, match="Deck 'Test Deck' already exists"):
            Deck.create("Test Deck")
    
    def test_deck_name_trimming(self):
        """Test that deck names are trimmed of whitespace."""
        deck = Deck.create("  Spanish Verbs  ")
        assert deck.name == "Spanish Verbs"
    
    def test_get_deck_by_id(self):
        """Test retrieving a deck by ID."""
        original_deck = Deck.create("Test Deck")
        
        retrieved_deck = Deck.get_by_id(original_deck.id)
        
        assert retrieved_deck is not None
        assert retrieved_deck.id == original_deck.id
        assert retrieved_deck.name == original_deck.name
        assert retrieved_deck.created_at == original_deck.created_at
    
    def test_get_deck_by_name(self):
        """Test retrieving a deck by name."""
        original_deck = Deck.create("Test Deck")
        
        retrieved_deck = Deck.get_by_name("Test Deck")
        
        assert retrieved_deck is not None
        assert retrieved_deck.id == original_deck.id
        assert retrieved_deck.name == original_deck.name
    
    def test_get_nonexistent_deck(self):
        """Test retrieving a non-existent deck returns None."""
        assert Deck.get_by_id(999) is None
        assert Deck.get_by_name("Nonexistent") is None
    
    def test_get_all_decks(self):
        """Test retrieving all decks."""
        deck1 = Deck.create("Deck A")
        deck2 = Deck.create("Deck B")
        deck3 = Deck.create("Deck C")
        
        all_decks = Deck.get_all()
        
        assert len(all_decks) == 3
        deck_names = [deck.name for deck in all_decks]
        assert "Deck A" in deck_names
        assert "Deck B" in deck_names
        assert "Deck C" in deck_names
        
        # Should be ordered by name
        assert all_decks[0].name == "Deck A"
        assert all_decks[1].name == "Deck B"
        assert all_decks[2].name == "Deck C"
    
    def test_deck_deletion(self):
        """Test deleting a deck."""
        deck = Deck.create("Test Deck")
        deck_id = deck.id
        
        # Delete the deck
        result = deck.delete()
        assert result is True
        
        # Verify it's gone
        assert Deck.get_by_id(deck_id) is None
    
    def test_deck_deletion_cascades_to_cards(self):
        """Test that deleting a deck also deletes its cards."""
        deck = Deck.create("Test Deck")
        card = Card.create(deck.id, "front", "back")
        
        # Delete the deck
        deck.delete()
        
        # Verify card is also deleted
        assert Card.get_by_id(card.id) is None
    
    def test_deck_card_counts(self):
        """Test getting card counts for a deck."""
        deck = Deck.create("Test Deck")
        
        # Initially empty
        counts = deck.get_card_counts()
        assert counts['total'] == 0
        assert counts['due'] == 0
        assert counts['new'] == 0
        
        # Add some new cards (repetitions = 0)
        Card.create(deck.id, "front1", "back1")  # New card
        Card.create(deck.id, "front2", "back2")  # New card

        # Create a reviewed card that's due (repetitions > 0)
        Card.create(deck.id, "front3", "back3")  # This will be due by default

        # Check counts - all cards are initially due, 3 are new
        counts = deck.get_card_counts()
        assert counts['total'] == 3
        assert counts['due'] == 3  # All cards are due initially
        assert counts['new'] == 3  # All cards are new (repetitions = 0)


class TestCard:
    """Test Card model functionality."""
    
    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)
        self.deck = Deck.create("Test Deck")
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
    
    def test_card_creation_with_valid_data(self):
        """Test creating a card with valid data."""
        card = Card.create(self.deck.id, "What is 2+2?", "4")
        
        assert card.id is not None
        assert card.deck_id == self.deck.id
        assert card.front == "What is 2+2?"
        assert card.back == "4"
        assert card.interval == 0
        assert card.repetitions == 0
        assert card.ease_factor == 2.5
        assert card.due_date is not None
        assert card.created_at is not None
    
    def test_card_creation_with_invalid_data(self):
        """Test card creation validation with invalid data."""
        # Empty front
        with pytest.raises(ValueError, match="Card front cannot be empty"):
            Card.create(self.deck.id, "", "back")
        
        # Empty back
        with pytest.raises(ValueError, match="Card back cannot be empty"):
            Card.create(self.deck.id, "front", "")
        
        # Whitespace only
        with pytest.raises(ValueError, match="Card front cannot be empty"):
            Card.create(self.deck.id, "   ", "back")
    
    def test_card_content_trimming(self):
        """Test that card content is trimmed of whitespace."""
        card = Card.create(self.deck.id, "  front  ", "  back  ")
        assert card.front == "front"
        assert card.back == "back"
    
    def test_unicode_support_in_card_content(self):
        """Test that cards support Unicode characters."""
        card = Card.create(self.deck.id, "¿Cómo estás?", "How are you? 你好")
        
        assert card.front == "¿Cómo estás?"
        assert card.back == "How are you? 你好"
        
        # Verify it persists correctly
        retrieved_card = Card.get_by_id(card.id)
        assert retrieved_card.front == "¿Cómo estás?"
        assert retrieved_card.back == "How are you? 你好"
    
    def test_get_card_by_id(self):
        """Test retrieving a card by ID."""
        original_card = Card.create(self.deck.id, "front", "back")
        
        retrieved_card = Card.get_by_id(original_card.id)
        
        assert retrieved_card is not None
        assert retrieved_card.id == original_card.id
        assert retrieved_card.front == original_card.front
        assert retrieved_card.back == original_card.back
        assert retrieved_card.deck_id == original_card.deck_id
    
    def test_get_cards_by_deck(self):
        """Test retrieving all cards in a deck."""
        card1 = Card.create(self.deck.id, "front1", "back1")
        card2 = Card.create(self.deck.id, "front2", "back2")
        card3 = Card.create(self.deck.id, "front3", "back3")
        
        cards = Card.get_by_deck(self.deck.id)
        
        assert len(cards) == 3
        card_fronts = [card.front for card in cards]
        assert "front1" in card_fronts
        assert "front2" in card_fronts
        assert "front3" in card_fronts
    
    def test_get_due_cards(self):
        """Test retrieving due cards."""
        # Create cards with different due dates
        card1 = Card.create(self.deck.id, "due1", "back1")
        card1.due_date = datetime.now() - timedelta(hours=2)  # Due (more buffer)
        card1.save()

        card2 = Card.create(self.deck.id, "due2", "back2")
        card2.due_date = datetime.now() - timedelta(days=1)  # Due
        card2.save()

        card3 = Card.create(self.deck.id, "future", "back3")
        card3.due_date = datetime.now() + timedelta(hours=2)  # Not due (more buffer)
        card3.save()

        # Get due cards for this deck
        due_cards = Card.get_due_cards(self.deck.id)

        assert len(due_cards) == 2
        due_fronts = [card.front for card in due_cards]
        assert "due1" in due_fronts
        assert "due2" in due_fronts
        assert "future" not in due_fronts

        # Should be ordered by due date (oldest first)
        assert due_cards[0].front == "due2"  # Older due date
        assert due_cards[1].front == "due1"  # Newer due date
    
    def test_get_all_due_cards(self):
        """Test retrieving due cards across all decks."""
        # Create another deck
        deck2 = Deck.create("Deck 2")
        
        # Create due cards in both decks
        card1 = Card.create(self.deck.id, "deck1_due", "back1")
        card1.due_date = datetime.now() - timedelta(hours=1)
        card1.save()
        
        card2 = Card.create(deck2.id, "deck2_due", "back2")
        card2.due_date = datetime.now() - timedelta(hours=2)
        card2.save()
        
        # Get all due cards
        all_due_cards = Card.get_due_cards()
        
        assert len(all_due_cards) == 2
        due_fronts = [card.front for card in all_due_cards]
        assert "deck1_due" in due_fronts
        assert "deck2_due" in due_fronts
    
    def test_card_save_functionality(self):
        """Test saving card changes."""
        card = Card.create(self.deck.id, "original", "original")
        original_id = card.id
        
        # Modify the card
        card.front = "modified"
        card.back = "modified"
        card.interval = 1440  # 1 day
        card.repetitions = 1
        card.ease_factor = 2.6
        card.due_date = datetime.now() + timedelta(days=1)
        
        # Save changes
        card.save()
        
        # Retrieve and verify changes
        retrieved_card = Card.get_by_id(original_id)
        assert retrieved_card.front == "modified"
        assert retrieved_card.back == "modified"
        assert retrieved_card.interval == 1440
        assert retrieved_card.repetitions == 1
        assert retrieved_card.ease_factor == 2.6


class TestReview:
    """Test Review model functionality."""
    
    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)
        self.deck = Deck.create("Test Deck")
        self.card = Card.create(self.deck.id, "front", "back")
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
    
    def test_review_creation_with_valid_data(self):
        """Test creating a review with valid data."""
        review = Review.create(self.card.id, 3)
        
        assert review.id is not None
        assert review.card_id == self.card.id
        assert review.rating == 3
        assert review.reviewed_at is not None
        assert isinstance(review.reviewed_at, datetime)
    
    def test_review_creation_with_invalid_rating(self):
        """Test review creation validation with invalid ratings."""
        # Invalid ratings
        with pytest.raises(ValueError, match="Rating must be 1, 2, 3, or 4"):
            Review.create(self.card.id, 0)
        
        with pytest.raises(ValueError, match="Rating must be 1, 2, 3, or 4"):
            Review.create(self.card.id, 5)
    
    def test_review_creation_with_all_valid_ratings(self):
        """Test creating reviews with all valid ratings."""
        for rating in [1, 2, 3, 4]:
            review = Review.create(self.card.id, rating)
            assert review.rating == rating
    
    def test_get_reviews_by_card(self):
        """Test retrieving all reviews for a card."""
        # Create multiple reviews
        review1 = Review.create(self.card.id, 1)
        review2 = Review.create(self.card.id, 3)
        review3 = Review.create(self.card.id, 4)
        
        # Get reviews for the card
        reviews = Review.get_by_card(self.card.id)
        
        assert len(reviews) == 3
        ratings = [review.rating for review in reviews]
        assert 1 in ratings
        assert 3 in ratings
        assert 4 in ratings
        
        # Should be ordered by reviewed_at DESC (newest first)
        assert reviews[0].id == review3.id  # Most recent
        assert reviews[1].id == review2.id
        assert reviews[2].id == review1.id  # Oldest
    
    def test_review_history_tracking(self):
        """Test that review history is properly tracked."""
        # Create reviews over time
        review1 = Review.create(self.card.id, 2)
        time1 = review1.reviewed_at
        
        # Small delay to ensure different timestamps
        import time
        time.sleep(0.01)
        
        review2 = Review.create(self.card.id, 4)
        time2 = review2.reviewed_at
        
        assert time2 > time1
        
        # Verify both reviews are tracked
        reviews = Review.get_by_card(self.card.id)
        assert len(reviews) == 2


class TestModelRelationships:
    """Test relationships between models."""
    
    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
    
    def test_deck_card_relationship(self):
        """Test the relationship between decks and cards."""
        deck = Deck.create("Test Deck")
        card1 = Card.create(deck.id, "front1", "back1")
        card2 = Card.create(deck.id, "front2", "back2")
        
        # Get cards for the deck
        deck_cards = Card.get_by_deck(deck.id)
        assert len(deck_cards) == 2
        
        # Verify card deck_id references
        assert card1.deck_id == deck.id
        assert card2.deck_id == deck.id
    
    def test_card_review_relationship(self):
        """Test the relationship between cards and reviews."""
        deck = Deck.create("Test Deck")
        card = Card.create(deck.id, "front", "back")
        
        review1 = Review.create(card.id, 2)
        review2 = Review.create(card.id, 4)
        
        # Get reviews for the card
        card_reviews = Review.get_by_card(card.id)
        assert len(card_reviews) == 2
        
        # Verify review card_id references
        assert review1.card_id == card.id
        assert review2.card_id == card.id
    
    def test_cascading_deletes(self):
        """Test that deleting a deck cascades to cards and reviews."""
        deck = Deck.create("Test Deck")
        card = Card.create(deck.id, "front", "back")
        Review.create(card.id, 3)
        
        # Store IDs for verification
        card_id = card.id
        
        # Delete the deck
        deck.delete()
        
        # Verify card and review are also deleted
        assert Card.get_by_id(card_id) is None
        
        # Check that reviews are also gone (via foreign key cascade)
        reviews = Review.get_by_card(card_id)
        assert len(reviews) == 0


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
