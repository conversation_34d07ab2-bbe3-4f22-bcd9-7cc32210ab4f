"""
Test database layer functionality.

This module tests the database operations, schema creation,
connection management, and data persistence.
"""

import pytest
import tempfile
import sqlite3
import threading
import time
from pathlib import Path
from unittest.mock import patch

from srs.database import Database, get_database, reset_database


class TestDatabase:
    """Test Database class functionality."""
    
    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()  # Reset global instance
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
    
    def test_database_creation_on_first_run(self):
        """Test that database is created automatically on first run."""
        # Ensure database file doesn't exist
        db_file = Path(self.db_path)
        assert not db_file.exists()
        
        # Create database instance
        db = Database(self.db_path)
        
        # Check that file was created
        assert db_file.exists()
        assert db_file.stat().st_size > 0
    
    def test_schema_creation(self):
        """Test that all tables and indexes are created correctly."""
        db = Database(self.db_path)
        
        # Check that tables exist
        tables = db.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table'"
        )
        table_names = [row['name'] for row in tables]
        
        assert 'decks' in table_names
        assert 'cards' in table_names
        assert 'reviews' in table_names
    
    def test_indexes_creation(self):
        """Test that performance indexes are created."""
        db = Database(self.db_path)
        
        # Check that indexes exist
        indexes = db.execute_query(
            "SELECT name FROM sqlite_master WHERE type='index'"
        )
        index_names = [row['name'] for row in indexes]
        
        assert 'idx_due_cards' in index_names
        assert 'idx_deck_cards' in index_names
        assert 'idx_card_reviews' in index_names
    
    def test_foreign_key_constraints(self):
        """Test that foreign key constraints are enforced."""
        db = Database(self.db_path)
        
        # Check that foreign keys are enabled
        result = db.execute_query("PRAGMA foreign_keys")
        assert result[0][0] == 1  # Foreign keys should be ON
        
        # Test constraint enforcement
        with pytest.raises(sqlite3.IntegrityError):
            db.execute_update(
                "INSERT INTO cards (deck_id, front, back) VALUES (?, ?, ?)",
                (999, "test front", "test back")  # Non-existent deck_id
            )
    
    def test_data_persistence_between_sessions(self):
        """Test that data persists between database sessions."""
        # Create first database instance and add data
        db1 = Database(self.db_path)
        deck_id = db1.execute_update(
            "INSERT INTO decks (name) VALUES (?)",
            ("Test Deck",)
        )
        
        # Close first instance
        db1.close()
        
        # Create second instance and verify data exists
        db2 = Database(self.db_path)
        result = db2.execute_query(
            "SELECT name FROM decks WHERE id = ?",
            (deck_id,)
        )
        
        assert len(result) == 1
        assert result[0]['name'] == "Test Deck"
    
    def test_concurrent_access_safety(self):
        """Test that database handles concurrent access safely."""
        db = Database(self.db_path)
        results = []
        errors = []
        
        def worker(worker_id):
            try:
                # Each worker creates a deck
                deck_id = db.execute_update(
                    "INSERT INTO decks (name) VALUES (?)",
                    (f"Deck {worker_id}",)
                )
                results.append(deck_id)
            except Exception as e:
                errors.append(e)
        
        # Start multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Check results
        assert len(errors) == 0, f"Errors occurred: {errors}"
        assert len(results) == 5
        assert len(set(results)) == 5  # All IDs should be unique
    
    def test_transaction_rollback(self):
        """Test that transactions roll back on error."""
        db = Database(self.db_path)
        
        # Create a deck first
        deck_id = db.execute_update(
            "INSERT INTO decks (name) VALUES (?)",
            ("Test Deck",)
        )
        
        # Try to insert invalid data in a transaction
        try:
            with db.transaction() as conn:
                # This should succeed
                conn.execute(
                    "INSERT INTO cards (deck_id, front, back) VALUES (?, ?, ?)",
                    (deck_id, "front1", "back1")
                )
                
                # This should fail (invalid deck_id)
                conn.execute(
                    "INSERT INTO cards (deck_id, front, back) VALUES (?, ?, ?)",
                    (999, "front2", "back2")
                )
        except sqlite3.IntegrityError:
            pass  # Expected error
        
        # Check that no cards were inserted (transaction rolled back)
        result = db.execute_query(
            "SELECT COUNT(*) as count FROM cards WHERE deck_id = ?",
            (deck_id,)
        )
        assert result[0]['count'] == 0
    
    def test_database_corruption_handling(self):
        """Test graceful handling of corrupted database files."""
        # Create a corrupted database file
        with open(self.db_path, 'w') as f:
            f.write("This is not a valid SQLite database")
        
        # Database should handle this gracefully
        with pytest.raises(sqlite3.DatabaseError):
            Database(self.db_path)
    
    def test_backup_restore_functionality(self):
        """Test database backup and restore functionality."""
        # Create database with data
        db = Database(self.db_path)
        deck_id = db.execute_update(
            "INSERT INTO decks (name) VALUES (?)",
            ("Test Deck",)
        )
        
        # Create backup path
        backup_path = str(Path(self.temp_dir) / "backup.db")
        
        # Backup database
        backup_conn = sqlite3.connect(backup_path)
        db.connection.backup(backup_conn)
        backup_conn.close()
        
        # Verify backup contains data
        backup_db = Database(backup_path)
        result = backup_db.execute_query(
            "SELECT name FROM decks WHERE id = ?",
            (deck_id,)
        )
        
        assert len(result) == 1
        assert result[0]['name'] == "Test Deck"
    
    def test_query_timeout(self):
        """Test query timeout functionality."""
        db = Database(self.db_path)

        # Test that connection was created with timeout parameter
        # We can't directly access the timeout, but we can verify the connection works
        result = db.execute_query("SELECT 1 as test")
        assert len(result) == 1
        assert result[0]['test'] == 1
    
    def test_row_factory_configuration(self):
        """Test that row factory is configured for dict-like access."""
        db = Database(self.db_path)
        
        # Insert test data
        deck_id = db.execute_update(
            "INSERT INTO decks (name) VALUES (?)",
            ("Test Deck",)
        )
        
        # Query and verify row factory
        result = db.execute_query(
            "SELECT id, name FROM decks WHERE id = ?",
            (deck_id,)
        )
        
        assert len(result) == 1
        row = result[0]
        
        # Should be able to access by column name
        assert row['id'] == deck_id
        assert row['name'] == "Test Deck"


class TestDatabaseGlobalInstance:
    """Test global database instance management."""
    
    def setup_method(self):
        """Set up for each test."""
        reset_database()
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
    
    def test_get_database_singleton(self):
        """Test that get_database returns the same instance."""
        db1 = get_database()
        db2 = get_database()
        
        assert db1 is db2
    
    def test_get_database_with_custom_path(self):
        """Test get_database with custom path."""
        temp_dir = tempfile.mkdtemp()
        custom_path = str(Path(temp_dir) / "custom.db")
        
        db = get_database(custom_path)
        assert db.db_path == custom_path
    
    def test_reset_database(self):
        """Test that reset_database clears the global instance."""
        db1 = get_database()
        reset_database()
        db2 = get_database()
        
        assert db1 is not db2


class TestDatabasePerformance:
    """Test database performance requirements."""
    
    def setup_method(self):
        """Set up test database."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "perf_test.db")
        self.db = Database(self.db_path)
        reset_database()
    
    def teardown_method(self):
        """Clean up."""
        reset_database()
    
    def test_database_operation_performance(self):
        """Test that database operations complete in under 100ms."""
        # Create test data
        deck_id = self.db.execute_update(
            "INSERT INTO decks (name) VALUES (?)",
            ("Performance Test",)
        )
        
        # Test query performance
        start_time = time.time()
        result = self.db.execute_query(
            "SELECT * FROM decks WHERE id = ?",
            (deck_id,)
        )
        query_time = time.time() - start_time
        
        assert query_time < 0.1  # Less than 100ms
        assert len(result) == 1
        
        # Test insert performance
        start_time = time.time()
        card_id = self.db.execute_update(
            "INSERT INTO cards (deck_id, front, back) VALUES (?, ?, ?)",
            (deck_id, "test front", "test back")
        )
        insert_time = time.time() - start_time
        
        assert insert_time < 0.1  # Less than 100ms
        assert card_id is not None
    
    def test_large_dataset_handling(self):
        """Test database performance with larger datasets."""
        # Create deck
        deck_id = self.db.execute_update(
            "INSERT INTO decks (name) VALUES (?)",
            ("Large Dataset Test",)
        )
        
        # Insert many cards
        start_time = time.time()
        for i in range(1000):
            self.db.execute_update(
                "INSERT INTO cards (deck_id, front, back) VALUES (?, ?, ?)",
                (deck_id, f"front {i}", f"back {i}")
            )
        insert_time = time.time() - start_time

        # Verify insert performance was reasonable
        assert insert_time < 10.0  # Should complete within 10 seconds

        # Query all cards
        start_time = time.time()
        result = self.db.execute_query(
            "SELECT COUNT(*) as count FROM cards WHERE deck_id = ?",
            (deck_id,)
        )
        query_time = time.time() - start_time
        
        assert result[0]['count'] == 1000
        assert query_time < 0.1  # Query should still be fast
        
        # Test index effectiveness with due cards query
        start_time = time.time()
        due_cards = self.db.execute_query(
            "SELECT * FROM cards WHERE deck_id = ? AND due_date <= datetime('now')",
            (deck_id,)
        )
        due_query_time = time.time() - start_time
        
        assert due_query_time < 0.1  # Index should make this fast
        assert len(due_cards) == 1000  # All cards should be due initially


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
