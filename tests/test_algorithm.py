"""
Test SM-2 algorithm functionality.

This module tests the SM-2 spaced repetition algorithm implementation
including learning steps, interval calculations, ease factor adjustments,
and edge cases.
"""

import pytest
import tempfile
from datetime import datetime, timedelta
from pathlib import Path

from srs.database import reset_database, get_database
from srs.models import Card, Deck
from srs.algorithm import SM2Algorithm, SM2Config, get_algorithm, reset_algorithm


class TestSM2Config:
    """Test SM-2 configuration."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = SM2Config()
        
        assert config.initial_ease_factor == 2.5
        assert config.min_ease_factor == 1.3
        assert config.max_ease_factor == 2.5
        assert config.learning_steps == [1, 10]  # minutes
        assert config.min_interval == 1  # day
        assert config.max_interval == 365  # days
        assert config.easy_bonus == 1.3
        assert config.fuzz_range == 0.05  # ±5%
    
    def test_custom_config(self):
        """Test custom configuration values."""
        config = SM2Config(
            initial_ease_factor=2.0,
            learning_steps=[5, 15],
            easy_bonus=1.5
        )
        
        assert config.initial_ease_factor == 2.0
        assert config.learning_steps == [5, 15]
        assert config.easy_bonus == 1.5
        # Other values should remain default
        assert config.min_ease_factor == 1.3


class TestSM2Algorithm:
    """Test SM-2 algorithm implementation."""
    
    def setup_method(self):
        """Set up test database and algorithm for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        reset_algorithm()
        
        # Force the models to use our test database
        self.db = get_database(self.db_path)
        self.deck = Deck.create("Test Deck")
        
        # Create algorithm with default config
        self.algorithm = SM2Algorithm()
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
        reset_algorithm()
    
    def test_algorithm_initialization(self):
        """Test algorithm initialization with default config."""
        algorithm = SM2Algorithm()
        
        assert algorithm.config.initial_ease_factor == 2.5
        assert algorithm.config.learning_steps == [1, 10]
    
    def test_algorithm_initialization_with_custom_config(self):
        """Test algorithm initialization with custom config."""
        config = SM2Config(learning_steps=[2, 20])
        algorithm = SM2Algorithm(config)
        
        assert algorithm.config.learning_steps == [2, 20]
    
    def test_invalid_rating_raises_error(self):
        """Test that invalid ratings raise ValueError."""
        card = Card.create(self.deck.id, "front", "back")
        
        with pytest.raises(ValueError, match="Rating must be 1, 2, 3, or 4"):
            self.algorithm.review_card(card, 0)
        
        with pytest.raises(ValueError, match="Rating must be 1, 2, 3, or 4"):
            self.algorithm.review_card(card, 5)
    
    def test_new_card_learning_steps_progression(self):
        """Test new card progression through learning steps."""
        card = Card.create(self.deck.id, "front", "back")
        
        # First review (rating 3) - should go to first learning step
        result = self.algorithm.review_card(card, 3)
        assert result.interval == 1  # 1 minute
        assert result.repetitions == 0
        assert result.ease_factor == 2.5
        
        # Update card with result
        card.interval = result.interval
        card.repetitions = result.repetitions
        card.ease_factor = result.ease_factor
        card.due_date = result.due_date
        
        # Second review (rating 3) - should go to second learning step
        result = self.algorithm.review_card(card, 3)
        assert result.interval == 10  # 10 minutes
        assert result.repetitions == 0
        
        # Update card again
        card.interval = result.interval
        card.repetitions = result.repetitions
        card.ease_factor = result.ease_factor
        card.due_date = result.due_date
        
        # Third review (rating 3) - should graduate to 1 day
        result = self.algorithm.review_card(card, 3)
        assert result.interval == 1440  # 1 day in minutes
        assert result.repetitions == 1
    
    def test_new_card_learning_failure(self):
        """Test new card failure (rating 1) restarts learning."""
        card = Card.create(self.deck.id, "front", "back")
        
        # First review (rating 3) - advance to step 1
        result = self.algorithm.review_card(card, 3)
        card.interval = result.interval
        card.repetitions = result.repetitions
        
        # Second review (rating 1) - should restart
        result = self.algorithm.review_card(card, 1)
        assert result.interval == 1  # Back to 1 minute
        assert result.repetitions == 0
    
    def test_sm2_interval_calculation_rating_1(self):
        """Test SM-2 interval calculation for rating 1 (Again)."""
        # Create a graduated card
        card = Card.create(self.deck.id, "front", "back")
        card.interval = 1440  # 1 day in minutes
        card.repetitions = 1
        card.ease_factor = 2.5
        
        # Rating 1 should reset to learning
        result = self.algorithm.review_card(card, 1)
        assert result.interval == 1  # Back to 1 minute
        assert result.repetitions == 0
        assert result.ease_factor < 2.5  # Ease factor should decrease
    
    def test_sm2_interval_calculation_rating_2(self):
        """Test SM-2 interval calculation for rating 2 (Hard)."""
        # Create a graduated card
        card = Card.create(self.deck.id, "front", "back")
        card.interval = 1440  # 1 day in minutes
        card.repetitions = 1
        card.ease_factor = 2.5
        
        # Rating 2 should decrease ease factor and calculate new interval
        result = self.algorithm.review_card(card, 2)
        assert result.ease_factor < 2.5  # Ease factor should decrease
        assert result.repetitions == 2
        assert result.interval > 1440  # Should be longer than previous
    
    def test_sm2_interval_calculation_rating_3(self):
        """Test SM-2 interval calculation for rating 3 (Good)."""
        # Create a graduated card
        card = Card.create(self.deck.id, "front", "back")
        card.interval = 1440  # 1 day in minutes
        card.repetitions = 1
        card.ease_factor = 2.5
        
        # Rating 3 should slightly decrease ease factor (SM-2 behavior)
        result = self.algorithm.review_card(card, 3)
        assert result.ease_factor < 2.5  # Ease factor should decrease slightly
        assert result.repetitions == 2
        assert result.interval > 1440  # Should be longer than previous
    
    def test_sm2_interval_calculation_rating_4(self):
        """Test SM-2 interval calculation for rating 4 (Easy)."""
        # Create a graduated card
        card = Card.create(self.deck.id, "front", "back")
        card.interval = 1440  # 1 day in minutes
        card.repetitions = 1
        card.ease_factor = 2.5
        
        # Rating 4 should maintain ease factor (SM-2 behavior) and apply easy bonus
        result = self.algorithm.review_card(card, 4)
        assert result.ease_factor == 2.5  # Ease factor should stay same for rating 4
        assert result.repetitions == 2
        assert result.interval > 1440  # Should be longer than previous
        
        # Should be longer than rating 3 due to easy bonus
        card_copy = Card.create(self.deck.id, "front2", "back2")
        card_copy.interval = 1440
        card_copy.repetitions = 1
        card_copy.ease_factor = 2.5
        result_3 = self.algorithm.review_card(card_copy, 3)
        
        assert result.interval > result_3.interval  # Easy bonus effect
    
    def test_ease_factor_adjustments(self):
        """Test ease factor adjustments for all ratings."""
        initial_ef = 2.5
        
        # Test rating 1 (Again) - should decrease significantly
        card1 = Card.create(self.deck.id, "front1", "back1")
        card1.ease_factor = initial_ef
        card1.interval = 1440
        card1.repetitions = 1
        result1 = self.algorithm.review_card(card1, 1)
        assert result1.ease_factor < initial_ef
        
        # Test rating 2 (Hard) - should decrease
        card2 = Card.create(self.deck.id, "front2", "back2")
        card2.ease_factor = initial_ef
        card2.interval = 1440
        card2.repetitions = 1
        result2 = self.algorithm.review_card(card2, 2)
        assert result2.ease_factor < initial_ef
        
        # Test rating 3 (Good) - should decrease slightly (SM-2 behavior)
        card3 = Card.create(self.deck.id, "front3", "back3")
        card3.ease_factor = initial_ef
        card3.interval = 1440
        card3.repetitions = 1
        result3 = self.algorithm.review_card(card3, 3)
        assert result3.ease_factor < initial_ef  # Should decrease
        assert result3.ease_factor > initial_ef - 0.2  # But not too much

        # Test rating 4 (Easy) - should stay same (SM-2 behavior)
        card4 = Card.create(self.deck.id, "front4", "back4")
        card4.ease_factor = initial_ef
        card4.interval = 1440
        card4.repetitions = 1
        result4 = self.algorithm.review_card(card4, 4)
        assert result4.ease_factor == initial_ef  # Should stay same
    
    def test_interval_bounds_enforcement(self):
        """Test that intervals respect min/max bounds."""
        # Test minimum interval
        card = Card.create(self.deck.id, "front", "back")
        card.ease_factor = 1.3  # Very low ease factor
        card.interval = 1440  # 1 day
        card.repetitions = 1
        
        result = self.algorithm.review_card(card, 2)  # Hard rating
        interval_days = result.interval / (24 * 60)
        assert interval_days >= self.algorithm.config.min_interval
        
        # Test maximum interval (simulate many reviews)
        card2 = Card.create(self.deck.id, "front2", "back2")
        card2.ease_factor = 2.5
        card2.interval = 300 * 24 * 60  # 300 days in minutes
        card2.repetitions = 10
        
        result2 = self.algorithm.review_card(card2, 4)  # Easy rating
        interval_days = result2.interval / (24 * 60)
        assert interval_days <= self.algorithm.config.max_interval
    
    def test_fuzz_factor_application(self):
        """Test that fuzz factor is applied to prevent clustering."""
        # Create multiple identical cards and review them
        results = []
        for i in range(10):
            card = Card.create(self.deck.id, f"front{i}", f"back{i}")
            card.interval = 1440  # 1 day
            card.repetitions = 1
            card.ease_factor = 2.5
            
            result = self.algorithm.review_card(card, 3)
            results.append(result.interval)
        
        # Check that not all intervals are identical (fuzz applied)
        unique_intervals = set(results)
        assert len(unique_intervals) > 1  # Should have some variation
        
        # Check that variation is within reasonable bounds (±5%)
        base_interval = results[0]
        for interval in results:
            variation = abs(interval - base_interval) / base_interval
            assert variation <= 0.1  # Within 10% (allowing for some randomness)
    
    def test_easy_bonus_calculation(self):
        """Test easy bonus multiplier application."""
        # Create two identical cards
        card1 = Card.create(self.deck.id, "front1", "back1")
        card1.interval = 1440  # 1 day
        card1.repetitions = 1
        card1.ease_factor = 2.5
        
        card2 = Card.create(self.deck.id, "front2", "back2")
        card2.interval = 1440  # 1 day
        card2.repetitions = 1
        card2.ease_factor = 2.5
        
        # Review one with Good (3), one with Easy (4)
        result_good = self.algorithm.review_card(card1, 3)
        result_easy = self.algorithm.review_card(card2, 4)
        
        # Easy should have longer interval due to bonus
        assert result_easy.interval > result_good.interval
        
        # The ratio should be approximately the easy bonus
        # Note: With difficulty adjustments, the ratio may be slightly less than the pure easy bonus
        ratio = result_easy.interval / result_good.interval
        assert ratio > 1.15  # Should be significantly longer (adjusted for difficulty tracking)
    
    def test_due_date_calculation(self):
        """Test that due dates are calculated correctly."""
        card = Card.create(self.deck.id, "front", "back")
        
        before_review = datetime.now()
        result = self.algorithm.review_card(card, 3)

        # Due date should be in the future
        assert result.due_date > before_review
        
        # Due date should be approximately interval minutes from now
        expected_due = before_review + timedelta(minutes=result.interval)
        time_diff = abs((result.due_date - expected_due).total_seconds())
        assert time_diff < 60  # Within 1 minute tolerance


class TestSM2AlgorithmEdgeCases:
    """Test edge cases and boundary conditions."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        reset_algorithm()
        
        self.db = get_database(self.db_path)
        self.deck = Deck.create("Test Deck")
        self.algorithm = SM2Algorithm()
    
    def teardown_method(self):
        """Clean up."""
        reset_database()
        reset_algorithm()
    
    def test_very_low_ease_factor(self):
        """Test behavior with very low ease factors."""
        card = Card.create(self.deck.id, "front", "back")
        card.ease_factor = 1.3  # Minimum ease factor
        card.interval = 1440
        card.repetitions = 1
        
        result = self.algorithm.review_card(card, 1)  # Again
        assert result.ease_factor >= self.algorithm.config.min_ease_factor
    
    def test_very_high_ease_factor(self):
        """Test behavior with very high ease factors."""
        card = Card.create(self.deck.id, "front", "back")
        card.ease_factor = 2.5  # Maximum ease factor
        card.interval = 1440
        card.repetitions = 1
        
        result = self.algorithm.review_card(card, 4)  # Easy
        assert result.ease_factor <= self.algorithm.config.max_ease_factor
    
    def test_zero_interval_card(self):
        """Test behavior with zero interval cards."""
        card = Card.create(self.deck.id, "front", "back")
        card.interval = 0
        card.repetitions = 0
        
        result = self.algorithm.review_card(card, 3)
        assert result.interval > 0
    
    def test_very_high_repetitions(self):
        """Test behavior with cards that have many repetitions."""
        card = Card.create(self.deck.id, "front", "back")
        card.interval = 100 * 24 * 60  # 100 days in minutes
        card.repetitions = 50  # Many repetitions
        card.ease_factor = 2.5
        
        result = self.algorithm.review_card(card, 3)
        assert result.repetitions == 51
        assert result.interval <= self.algorithm.config.max_interval * 24 * 60


class TestGlobalAlgorithmInstance:
    """Test global algorithm instance management."""
    
    def setup_method(self):
        """Set up for each test."""
        reset_algorithm()
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_algorithm()
    
    def test_get_algorithm_singleton(self):
        """Test that get_algorithm returns the same instance."""
        alg1 = get_algorithm()
        alg2 = get_algorithm()
        
        assert alg1 is alg2
    
    def test_get_algorithm_with_custom_config(self):
        """Test get_algorithm with custom config."""
        config = SM2Config(easy_bonus=1.5)
        alg = get_algorithm(config)
        
        assert alg.config.easy_bonus == 1.5
    
    def test_reset_algorithm(self):
        """Test that reset_algorithm clears the global instance."""
        alg1 = get_algorithm()
        reset_algorithm()
        alg2 = get_algorithm()
        
        assert alg1 is not alg2


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
